import React, { useState, useEffect } from "react";
import {
  Menu,
  X,
  Home,
  User,
  FileText,
  Briefcase,
  MessageSquare,
  Star,
  Github,
  Linkedin,
  Twitter,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Facebook,
  Instagram,
  Download,
  Zap,
} from "lucide-react";
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON>,
  Drawer<PERSON>rigger,
  DrawerTitle,
  DrawerDescription,
} from "./ui/drawer";
import { Avatar, AvatarImage, AvatarFallback } from "./ui/avatar";

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const [scrolled, setScrolled] = useState(false);
  const [typingIndex, setTypingIndex] = useState(0);

  const navItems = [
    { id: "home", label: "Home", icon: Home },
    { id: "about", label: "About", icon: User },
    { id: "resume", label: "Resume", icon: FileText },
    { id: "skills", label: "Skills", icon: Zap },
    { id: "portfolio", label: "Portfolio", icon: Briefcase },
    { id: "testimonials", label: "Testimonials", icon: Star },
    { id: "contact", label: "Contact", icon: MessageSquare },
  ];

  const socialLinks = [
    { icon: Facebook, href: "https://facebook.com", label: "Facebook" },
    { icon: Twitter, href: "https://twitter.com", label: "Twitter" },
    { icon: Instagram, href: "https://instagram.com", label: "Instagram" },
    { icon: Linkedin, href: "https://linkedin.com", label: "LinkedIn" },
  ];

  const contactInfo = [
    {
      icon: Phone,
      label: "Phone",
      value: "+************",
      href: "tel:+1234567890",
    },
    {
      icon: Mail,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
    },
    { icon: MapPin, label: "Location", value: "New York, USA", href: "#" },
    { icon: Calendar, label: "Birthday", value: "May 27, 1990", href: "#" },
  ];

  const profession = "Web Developer";

  useEffect(() => {
    // Handle typing animation for profession text in mobile menu
    if (mobileMenuOpen) {
      const typingInterval = setInterval(() => {
        setTypingIndex((prev) => {
          if (prev >= profession.length) {
            // Reset after showing full text for 1.5s
            setTimeout(() => setTypingIndex(0), 1500);
            return prev;
          }
          return prev + 1;
        });
      }, 150);

      return () => clearInterval(typingInterval);
    } else {
      // Reset typing index when menu is closed
      setTypingIndex(0);
    }
  }, [mobileMenuOpen]);

  useEffect(() => {
    const handleScroll = () => {
      // Update header background when scrolled
      setScrolled(window.scrollY > 50);

      // Update active section based on scroll position
      const sections = navItems.map((item) => document.getElementById(item.id));
      let currentActive = "home";

      sections.forEach((section) => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;
          if (
            window.scrollY >= sectionTop - 100 &&
            window.scrollY < sectionTop + sectionHeight - 100
          ) {
            currentActive = section.id;
          }
        }
      });

      setActiveSection(currentActive);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.classList.add("mobile-menu-open");
    } else {
      document.body.classList.remove("mobile-menu-open");
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove("mobile-menu-open");
    };
  }, [mobileMenuOpen]);

  // Handle smooth scroll to section (shared function)
  const scrollToSection = (id: string, isMobile: boolean = false) => {
    const element = document.getElementById(id);
    if (element) {
      // Calculate offset for fixed header - different for mobile and desktop
      const headerOffset = isMobile ? 80 : 100; // Slightly less offset for mobile
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: Math.max(0, offsetPosition), // Ensure we don't scroll to negative position
        behavior: "smooth",
      });
    } else {
      console.warn(`Element with id "${id}" not found`);
    }
  };

  // Handle desktop navigation click
  const handleDesktopNavClick = (id: string, event: React.MouseEvent) => {
    event.preventDefault(); // Prevent default anchor behavior
    scrollToSection(id, false); // false = not mobile
  };

  // Handle mobile menu link click - close drawer and scroll to section
  const handleMobileNavClick = (id: string, event: React.MouseEvent) => {
    console.log("Mobile nav clicked:", id); // Debug log
    event.preventDefault(); // Prevent default anchor behavior
    event.stopPropagation(); // Stop event bubbling

    // Add click feedback
    const button = event.currentTarget;
    button.classList.add("nav-item-clicked");
    setTimeout(() => button.classList.remove("nav-item-clicked"), 150);

    // Close mobile menu first
    setMobileMenuOpen(false);

    // Longer delay to allow menu to fully close before scrolling
    setTimeout(() => {
      console.log("Scrolling to section:", id); // Debug log
      scrollToSection(id, true); // true = mobile
    }, 300);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Escape" && mobileMenuOpen) {
      setMobileMenuOpen(false);
    }
  };

  return (
    <header
      className={`fixed top-0 right-0 left-0 lg:left-[380px] z-[100] transition-all duration-500 ${
        scrolled
          ? "stunning-header-scrolled backdrop-blur-xl shadow-2xl"
          : "stunning-header-transparent"
      } overflow-hidden`}
      style={{ position: "fixed" }}
    >
      {/* Stunning Background Effects */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Primary Gradient Background */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-100" : "opacity-0"
          } bg-gradient-to-r from-dark-bg/95 via-dark-lighter/90 to-dark-bg/95`}
        ></div>

        {/* Animated Gradient Overlay */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-100" : "opacity-30"
          } bg-gradient-to-r from-red/10 via-transparent to-red/10 animate-pulse`}
        ></div>

        {/* Subtle Pattern Overlay */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-20" : "opacity-10"
          } header-pattern`}
        ></div>

        {/* Glow Effect */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            scrolled ? "opacity-100" : "opacity-0"
          } bg-gradient-to-b from-red/5 via-transparent to-transparent`}
        ></div>

        {/* Floating Particles Effect */}
        <div
          className={`absolute inset-0 transition-opacity duration-700 ${
            scrolled ? "opacity-100" : "opacity-60"
          } header-particles`}
        >
          <div className="header-particle header-particle-1"></div>
          <div className="header-particle header-particle-2"></div>
          <div className="header-particle header-particle-3"></div>
        </div>
      </div>

      {/* Content Wrapper with proper z-index */}
      <div className="container mx-auto px-4 py-4 relative z-10">
        <div className="flex justify-between items-center">
          {/* Dynamic Section Title */}
          <div className="hidden lg:block">
            <h1 className="enhanced-header-title">
              {navItems.find((item) => item.id === activeSection)?.label ||
                "Home"}
              <div className="header-title-glow"></div>
              <div className="header-title-underline"></div>
            </h1>
          </div>

          {/* Mobile Section Title */}
          <div className="lg:hidden">
            <h1 className="enhanced-header-title-mobile">
              {navItems.find((item) => item.id === activeSection)?.label ||
                "Home"}
              <div className="header-title-underline-mobile"></div>
            </h1>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <Drawer
              open={mobileMenuOpen}
              onOpenChange={setMobileMenuOpen}
              direction="right"
            >
              <DrawerTrigger asChild>
                <button
                  className="relative w-14 h-14 flex flex-col justify-center items-center text-gray-300 hover:text-white focus:outline-none transition-all duration-700 group stunning-hamburger-btn overflow-hidden"
                  aria-label={
                    mobileMenuOpen
                      ? "Close navigation menu"
                      : "Open navigation menu"
                  }
                  aria-expanded={mobileMenuOpen}
                  aria-controls="mobile-navigation-menu"
                >
                  {/* Outer Rotating Ring */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-red via-white to-red p-0.5 opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-spin-slower">
                    <div className="w-full h-full rounded-2xl bg-dark-bg/90 backdrop-blur-sm"></div>
                  </div>

                  {/* Inner Glow Ring */}
                  <div className="absolute inset-1 rounded-xl bg-gradient-to-br from-red/30 via-transparent to-red/20 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

                  {/* Stunning Background Effects */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-red/15 via-purple-500/8 to-red/12 opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-lg"></div>
                  <div className="absolute inset-0 rounded-2xl border border-red/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700 shadow-lg shadow-red/20"></div>

                  {/* Enhanced Floating Particles */}
                  <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                    <div className="absolute top-2 right-2 w-1.5 h-1.5 bg-red/70 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-2 left-2 w-1 h-1 bg-white/50 rounded-full animate-pulse delay-500"></div>
                    <div className="absolute top-3 left-3 w-0.5 h-0.5 bg-red/50 rounded-full animate-pulse delay-1000"></div>
                    <div className="absolute bottom-3 right-3 w-0.5 h-0.5 bg-white/30 rounded-full animate-pulse delay-1500"></div>
                    <div className="absolute top-1/2 right-1 w-0.5 h-0.5 bg-red/40 rounded-full animate-pulse delay-2000"></div>
                  </div>

                  {/* Magical Sparkles */}
                  <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-1000">
                    <div className="absolute top-1 right-4 w-1 h-1 bg-yellow-400/60 rounded-full animate-ping delay-300"></div>
                    <div className="absolute bottom-1 left-4 w-0.5 h-0.5 bg-blue-400/40 rounded-full animate-ping delay-800"></div>
                  </div>

                  {/* Enhanced Hamburger Lines Container */}
                  <div className="relative z-20 w-8 h-6 flex flex-col justify-between">
                    {/* Top Line */}
                    <span
                      className={`enhanced-hamburger-line block w-full h-0.5 rounded-full transition-all duration-700 ease-out relative ${
                        mobileMenuOpen
                          ? "rotate-45 translate-y-2.5 bg-red shadow-xl shadow-red/60 scale-110"
                          : "bg-current group-hover:shadow-lg group-hover:shadow-red/40 group-hover:scale-105"
                      }`}
                    >
                      <div
                        className={`absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 ${
                          mobileMenuOpen ? "opacity-100" : ""
                        }`}
                      ></div>
                    </span>

                    {/* Middle Line */}
                    <span
                      className={`enhanced-hamburger-line block w-full h-0.5 rounded-full transition-all duration-700 ease-out relative ${
                        mobileMenuOpen
                          ? "opacity-0 scale-0 rotate-180"
                          : "bg-current group-hover:shadow-lg group-hover:shadow-red/40 group-hover:scale-105"
                      }`}
                    >
                      <div
                        className={`absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                      ></div>
                    </span>

                    {/* Bottom Line */}
                    <span
                      className={`enhanced-hamburger-line block w-full h-0.5 rounded-full transition-all duration-700 ease-out relative ${
                        mobileMenuOpen
                          ? "-rotate-45 -translate-y-2.5 bg-red shadow-xl shadow-red/60 scale-110"
                          : "bg-current group-hover:shadow-lg group-hover:shadow-red/40 group-hover:scale-105"
                      }`}
                    >
                      <div
                        className={`absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 ${
                          mobileMenuOpen ? "opacity-100" : ""
                        }`}
                      ></div>
                    </span>
                  </div>

                  {/* Enhanced Pulse Effect */}
                  <div
                    className={`absolute inset-0 rounded-2xl transition-all duration-500 ${
                      mobileMenuOpen
                        ? "bg-red/30 animate-ping shadow-2xl shadow-red/40"
                        : "opacity-0"
                    }`}
                  ></div>

                  {/* Ripple Effect on Click */}
                  <div
                    className={`absolute inset-0 rounded-2xl bg-gradient-to-r from-red/20 via-white/10 to-red/20 transition-all duration-1000 ${
                      mobileMenuOpen
                        ? "scale-150 opacity-0"
                        : "scale-100 opacity-0"
                    }`}
                  ></div>
                </button>
              </DrawerTrigger>

              <DrawerContent
                className="fixed inset-y-0 left-0 z-50 mobile-menu-full-height w-80 max-w-[80vw] border-r border-gray-800/50 bg-gradient-to-br from-dark-bg/95 to-dark-lighter/95 backdrop-blur-xl shadow-2xl transition-all duration-500 ease-out mobile-menu-content overflow-hidden"
                style={{
                  transform: mobileMenuOpen
                    ? "translateX(0)"
                    : "translateX(-100%)",
                }}
                onKeyDown={handleKeyDown}
              >
                {/* Accessibility components - visually hidden */}
                <DrawerTitle className="sr-only">Navigation Menu</DrawerTitle>
                <DrawerDescription className="sr-only">
                  Main navigation menu with links to different sections of the
                  website
                </DrawerDescription>

                <div className="flex flex-col mobile-menu-full-height relative overflow-hidden">
                  {/* Gradient Background Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-red/5 via-transparent to-red/10 pointer-events-none"></div>

                  {/* Header with Close Button */}
                  <div className="flex justify-end items-start p-4 pt-6 pr-6 relative z-10 flex-shrink-0">
                    <button
                      onClick={() => setMobileMenuOpen(false)}
                      className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-800/30 text-gray-300 hover:text-red hover:bg-red/10 focus:outline-none transition-all duration-300 backdrop-blur-sm hover:scale-110"
                      aria-label="Close navigation menu"
                    >
                      <X size={16} />
                    </button>
                  </div>

                  {/* Scrollable Content */}
                  <div className="flex-1 overflow-y-auto relative z-10 mobile-menu-scrollable">
                    {/* Enhanced Profile Section */}
                    <div className="px-6 pb-8 pt-4">
                      <div className="relative">
                        {/* Background Glow Effects */}
                        <div className="absolute inset-0 bg-gradient-to-br from-red/10 via-transparent to-red/5 rounded-2xl blur-xl"></div>
                        <div className="absolute inset-0 bg-gradient-to-t from-transparent via-red/5 to-transparent rounded-2xl"></div>

                        {/* Profile Container */}
                        <div className="relative bg-gradient-to-br from-gray-800/40 to-gray-900/60 backdrop-blur-sm rounded-2xl border border-red/20 p-6 text-center mobile-profile-container">
                          {/* Animated Border Glow */}
                          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-red/20 via-transparent to-red/20 opacity-0 mobile-profile-glow"></div>

                          {/* Profile Avatar */}
                          <div className="relative mx-auto w-20 h-20 mb-4 mobile-profile-avatar">
                            {/* Avatar Glow Ring */}
                            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red/30 via-red/10 to-red/30 blur-md animate-pulse"></div>
                            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-red/20 to-transparent"></div>

                            {/* Avatar Image */}
                            <Avatar className="relative w-full h-full ring-2 ring-red/30 shadow-2xl hover:scale-110 transition-all duration-500 mobile-avatar-image">
                              <AvatarImage
                                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&auto=format&q=80"
                                alt="John Smith"
                                className="object-cover"
                              />
                              <AvatarFallback className="bg-red/20 text-red font-bold text-lg">
                                JS
                              </AvatarFallback>
                            </Avatar>

                            {/* Floating Particles */}
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-red rounded-full animate-ping"></div>
                            <div className="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-red/60 rounded-full animate-pulse delay-300"></div>
                          </div>

                          {/* User Name */}
                          <div className="relative">
                            <h3 className="text-xl font-bold text-white mb-1 mobile-profile-name">
                              <span className="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent">
                                John Smith
                              </span>
                            </h3>
                            <div className="w-12 h-0.5 bg-gradient-to-r from-transparent via-red to-transparent mx-auto mb-2"></div>
                          </div>

                          {/* Professional Title */}
                          <div className="relative">
                            <p className="text-red/90 text-sm font-medium mb-2 mobile-profile-title">
                              Web Developer
                            </p>
                            <p className="text-gray-400 text-xs leading-relaxed mobile-profile-subtitle">
                              Crafting digital experiences with passion
                            </p>
                          </div>

                          {/* Status Indicator */}
                          <div className="flex items-center justify-center gap-2 mt-4">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span className="text-green-400 text-xs font-medium">
                              Available for work
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Navigation Links */}
                    <nav
                      className="px-6 pb-6"
                      id="mobile-navigation-menu"
                      role="navigation"
                      aria-label="Main navigation"
                    >
                      <ul className="flex flex-col space-y-2" role="list">
                        {navItems.map((item, index) => {
                          const IconComponent = item.icon;
                          return (
                            <li key={item.id} className="mobile-nav-item">
                              <button
                                onClick={(e) =>
                                  handleMobileNavClick(item.id, e)
                                }
                                className={`w-full flex items-center gap-4 p-4 rounded-xl transition-all duration-300 group transform hover:scale-105 ${
                                  activeSection === item.id
                                    ? "bg-red/20 text-red border border-red/30 shadow-lg shadow-red/10 nav-item-active"
                                    : "text-gray-300 hover:text-white hover:bg-gray-800/50 border border-transparent hover:border-gray-700/50"
                                }`}
                                aria-current={
                                  activeSection === item.id ? "page" : undefined
                                }
                                aria-label={`Navigate to ${item.label} section`}
                              >
                                <div
                                  className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 ${
                                    activeSection === item.id
                                      ? "bg-red/30 text-red"
                                      : "bg-gray-800/50 text-gray-400 group-hover:bg-red/20 group-hover:text-red"
                                  }`}
                                >
                                  <IconComponent size={18} />
                                </div>
                                <span className="font-medium text-base">
                                  {item.label}
                                </span>
                                <div
                                  className={`ml-auto w-2 h-2 rounded-full transition-all duration-300 ${
                                    activeSection === item.id
                                      ? "bg-red"
                                      : "bg-transparent"
                                  }`}
                                ></div>
                              </button>
                            </li>
                          );
                        })}
                      </ul>
                    </nav>

                    {/* Contact Information */}
                    <div className="px-6 pb-4">
                      <div className="border-t border-gray-800/50 pt-6">
                        <h4 className="text-gray-400 text-sm mb-4 text-center font-medium">
                          Contact Information
                        </h4>
                        <div className="space-y-3">
                          {contactInfo.map((contact, index) => {
                            const IconComponent = contact.icon;
                            return (
                              <a
                                key={index}
                                href={contact.href}
                                className="flex items-center gap-3 p-3 rounded-lg bg-gray-800/30 hover:bg-gray-800/50 transition-all duration-300 group"
                              >
                                <div className="w-8 h-8 rounded-lg bg-red/20 flex items-center justify-center text-red group-hover:bg-red/30 transition-colors duration-300">
                                  <IconComponent size={14} />
                                </div>
                                <div className="flex-1">
                                  <p className="text-gray-400 text-xs">
                                    {contact.label}
                                  </p>
                                  <p className="text-gray-200 text-sm font-medium">
                                    {contact.value}
                                  </p>
                                </div>
                              </a>
                            );
                          })}
                        </div>
                      </div>
                    </div>

                    {/* Social Links */}
                    <div className="px-6 pb-6">
                      <div className="border-t border-gray-800/50 pt-6">
                        <p className="text-gray-400 text-sm mb-4 text-center">
                          Connect with me
                        </p>
                        <div className="flex justify-center gap-3">
                          {socialLinks.map((social, index) => {
                            const IconComponent = social.icon;
                            return (
                              <a
                                key={index}
                                href={social.href}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="social-link w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-red hover:bg-red/20"
                                aria-label={social.label}
                              >
                                <IconComponent size={16} />
                              </a>
                            );
                          })}
                        </div>
                      </div>
                    </div>

                    {/* Download CV Button */}
                    <div className="px-6 pb-6">
                      <a
                        href="#"
                        className="w-full flex items-center justify-center gap-2 p-3 rounded-lg bg-red/20 text-red border border-red/30 hover:bg-red/30 transition-all duration-300 font-medium"
                      >
                        <Download size={16} />
                        <span>Download CV</span>
                      </a>
                    </div>
                  </div>
                </div>
              </DrawerContent>
            </Drawer>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:block">
            <ul className="flex space-x-1">
              {navItems.map((item) => (
                <li key={item.id}>
                  <button
                    onClick={(e) => handleDesktopNavClick(item.id, e)}
                    className={`nav-link-dash relative px-4 py-2 rounded-lg transition-all duration-300 hover:bg-red/10 ${
                      activeSection === item.id
                        ? "text-red bg-red/10 shadow-lg shadow-red/20"
                        : "text-gray-300 hover:text-white"
                    }`}
                  >
                    {item.label}
                    {activeSection === item.id && (
                      <span
                        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 h-0.5 bg-red rounded-full"
                        style={{
                          width: "0%",
                          animation: "nav-dash 0.5s forwards",
                        }}
                      ></span>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
