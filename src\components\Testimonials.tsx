import React, { useState, useEffect } from "react";
import { Star, Quote } from "lucide-react";

const Testimonials = () => {
  const [visibleCards, setVisibleCards] = useState<number[]>([]);

  // Enhanced testimonial data with more details
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      position: "CEO at Apple",
      company: "Apple Inc.",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&q=80",
      text: "<PERSON> was a real pleasure to work with and we look forward to working with him again. He's definitely the kind of designer you can trust with a project from start to finish.",
      rating: 5,
      project: "iOS App Design",
      duration: "3 months",
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "CTO at Google",
      company: "Google LLC",
      image:
        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=300&h=300&fit=crop&q=80",
      text: "Highly recommend <PERSON>. His expertise in web development is exceptional. He delivered beyond our expectations and was a pleasure to work with throughout the project.",
      rating: 5,
      project: "Web Platform",
      duration: "6 months",
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Marketing Director",
      company: "TechCorp",
      image:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=300&fit=crop&q=80",
      text: "Working with John was an excellent experience. He understood our requirements perfectly and delivered a beautiful website that exceeded our expectations.",
      rating: 4,
      project: "Brand Website",
      duration: "2 months",
    },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cardId = parseInt(
              entry.target.getAttribute("data-card-id") || "0"
            );
            setVisibleCards((prev) => [...prev, cardId]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const cards = document.querySelectorAll(".testimonial-card");
    cards.forEach((card) => observer.observe(card));

    return () => observer.disconnect();
  }, []);

  return (
    <section
      id="testimonials"
      className="section bg-gradient-to-br from-white via-gray-50 to-white dark:bg-dark-darker relative overflow-hidden"
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <div className="testimonials-bg-gradient"></div>
        <div className="testimonials-floating-particles"></div>
      </div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="enhanced-testimonials-header">
            <h2 className="enhanced-testimonials-title">
              <span className="testimonials-title-text">Testimonials</span>
              <div className="testimonials-title-glow"></div>
            </h2>
            <div className="testimonials-description-container">
              <p className="testimonials-description">
                What my clients say about my work and expertise.
              </p>
              <div className="testimonials-description-accent"></div>
            </div>
          </div>
        </div>

        {/* Enhanced Testimonials Grid */}
        <div className="enhanced-testimonials-grid">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              data-card-id={testimonial.id}
              className={`testimonial-card enhanced-testimonial-card ${
                visibleCards.includes(testimonial.id) ? "visible" : ""
              }`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Card Background Effects */}
              <div className="testimonial-card-bg"></div>
              <div className="testimonial-card-glow"></div>
              <div className="testimonial-card-particles"></div>

              {/* Quote Icon */}
              <div className="testimonial-quote-icon">
                <Quote size={24} className="text-red opacity-60" />
              </div>

              {/* Enhanced Rating */}
              <div className="enhanced-rating-container">
                <div className="rating-stars">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="star-wrapper">
                      <Star
                        size={18}
                        className={`enhanced-star ${
                          i < testimonial.rating ? "filled" : "empty"
                        }`}
                      />
                    </div>
                  ))}
                </div>
                <div className="rating-glow"></div>
              </div>

              {/* Enhanced Text */}
              <div className="testimonial-text-container">
                <p className="enhanced-testimonial-text">{testimonial.text}</p>
              </div>

              {/* Enhanced Client Info */}
              <div className="enhanced-client-info">
                <div className="client-avatar-container">
                  <div className="client-avatar-glow"></div>
                  <div className="client-avatar">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="avatar-image"
                      loading="lazy"
                    />
                  </div>
                  <div className="avatar-ring"></div>
                </div>

                <div className="client-details">
                  <h5 className="client-name">{testimonial.name}</h5>
                  <p className="client-position">{testimonial.position}</p>
                  <p className="client-company">{testimonial.company}</p>

                  {/* Project Badge */}
                  <div className="project-badge">
                    <span className="project-name">{testimonial.project}</span>
                    <span className="project-duration">
                      {testimonial.duration}
                    </span>
                  </div>
                </div>
              </div>

              {/* Hover Effects */}
              <div className="testimonial-hover-overlay"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
