import React from "react";
import ThemeToggle from "./ThemeToggle";

const Footer = () => {
  return (
    <footer className="bg-dark-lighter py-8 lg:py-12 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-red/5 via-transparent to-transparent pointer-events-none"></div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        {/* Main Footer Content */}
        <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
          {/* Copyright Section */}
          <div className="text-center lg:text-left">
            <p className="footer-text text-gray-300 text-sm md:text-base lg:text-lg">
              © {new Date().getFullYear()} - All rights reserved by{" "}
              <span className="footer-name text-red font-semibold">
                <PERSON>
              </span>
            </p>

            {/* Subtitle */}
            <div className="footer-subtitle mt-2">
              <p className="text-gray-400 text-xs md:text-sm">
                Crafted with passion and precision
              </p>
            </div>
          </div>

          {/* Theme Toggle Section */}
          <div className="theme-toggle-section flex items-center justify-center lg:justify-end">
            <div className="theme-toggle-wrapper p-4 rounded-2xl bg-gradient-to-br from-gray-800/30 to-gray-900/30 backdrop-blur-sm border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300 shadow-lg hover:shadow-xl">
              <ThemeToggle />
            </div>
          </div>
        </div>

        {/* Mobile Additional Content */}
        <div className="footer-mobile-divider mt-8 pt-6 border-t border-gray-700/30 lg:hidden">
          <div className="text-center">
            <p className="text-gray-500 text-xs">
              Switch between light and dark themes above
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
