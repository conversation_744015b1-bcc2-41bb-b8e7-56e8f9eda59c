import React, { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  MessageSquare,
  Star,
  Mail,
  Search,
  Filter,
  MoreVertical,
  Reply,
  Archive,
  Trash2,
  Eye,
  Clock,
  User,
  Calendar,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";

const AdminMessages = () => {
  const [activeTab, setActiveTab] = useState("messages");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMessage, setSelectedMessage] = useState(null);

  const [messages] = useState([
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      subject: "Project Inquiry",
      message: "Hi <PERSON>, I'm interested in discussing a web development project for my startup. Could we schedule a call?",
      date: "2024-01-15",
      time: "10:30 AM",
      status: "unread",
      priority: "high",
    },
    {
      id: 2,
      name: "Mike Wilson",
      email: "<EMAIL>",
      subject: "Collaboration Opportunity",
      message: "Hello! I represent a tech company looking for freelance developers. Your portfolio looks impressive.",
      date: "2024-01-14",
      time: "2:15 PM",
      status: "read",
      priority: "medium",
    },
    {
      id: 3,
      name: "Emily Davis",
      email: "<EMAIL>",
      subject: "Design Partnership",
      message: "I'm a UI/UX designer and would love to collaborate on projects. Let's connect!",
      date: "2024-01-13",
      time: "4:45 PM",
      status: "replied",
      priority: "low",
    },
  ]);

  const [testimonials] = useState([
    {
      id: 1,
      name: "Alex Thompson",
      company: "TechStart Inc.",
      rating: 5,
      message: "John delivered an exceptional website that exceeded our expectations. Highly recommended!",
      date: "2024-01-12",
      status: "approved",
      project: "E-commerce Platform",
    },
    {
      id: 2,
      name: "Lisa Chen",
      company: "Creative Agency",
      rating: 5,
      message: "Professional, creative, and delivered on time. Will definitely work with John again.",
      date: "2024-01-10",
      status: "pending",
      project: "Portfolio Website",
    },
    {
      id: 3,
      name: "David Rodriguez",
      company: "Local Business",
      rating: 4,
      message: "Great communication and solid technical skills. The website looks fantastic!",
      date: "2024-01-08",
      status: "approved",
      project: "Business Website",
    },
  ]);

  const messageStats = [
    {
      title: "Total Messages",
      value: "284",
      change: "+12.5%",
      icon: MessageSquare,
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "Unread Messages",
      value: "23",
      change: "****%",
      icon: Mail,
      color: "from-red to-red/80",
    },
    {
      title: "Testimonials",
      value: "47",
      change: "+15.3%",
      icon: Star,
      color: "from-yellow-500 to-yellow-600",
    },
    {
      title: "Response Rate",
      value: "94%",
      change: "****%",
      icon: CheckCircle,
      color: "from-green-500 to-green-600",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "unread":
        return "bg-red/20 text-red border-red/30";
      case "read":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "replied":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "approved":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red/20 text-red border-red/30";
      case "medium":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "low":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const handleMessageAction = (action: string, messageId: number) => {
    switch (action) {
      case "reply":
        toast.info(`Reply to message ${messageId} - Feature coming soon!`);
        break;
      case "archive":
        toast.success(`Message ${messageId} archived!`);
        break;
      case "delete":
        toast.error(`Message ${messageId} deleted!`);
        break;
      default:
        break;
    }
  };

  const renderMessages = () => (
    <div className="space-y-4">
      {messages.map((message) => (
        <div
          key={message.id}
          className="bg-dark-bg/50 border border-red/20 rounded-lg p-6 hover:border-red/40 transition-all cursor-pointer"
          onClick={() => setSelectedMessage(message)}
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {message.name.charAt(0)}
                </span>
              </div>
              <div>
                <h3 className="text-white font-semibold">{message.name}</h3>
                <p className="text-gray-400 text-sm">{message.email}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getPriorityColor(message.priority)}>
                {message.priority}
              </Badge>
              <Badge className={getStatusColor(message.status)}>
                {message.status}
              </Badge>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-dark-lighter border-red/20">
                  <DropdownMenuItem onClick={() => handleMessageAction("reply", message.id)}>
                    <Reply className="w-4 h-4 mr-2" />
                    Reply
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleMessageAction("archive", message.id)}>
                    <Archive className="w-4 h-4 mr-2" />
                    Archive
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleMessageAction("delete", message.id)}
                    className="text-red hover:text-red"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          <div className="mb-3">
            <h4 className="text-white font-medium mb-2">{message.subject}</h4>
            <p className="text-gray-300 line-clamp-2">{message.message}</p>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-400">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              {message.date}
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {message.time}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderTestimonials = () => (
    <div className="space-y-4">
      {testimonials.map((testimonial) => (
        <div
          key={testimonial.id}
          className="bg-dark-bg/50 border border-red/20 rounded-lg p-6 hover:border-red/40 transition-all"
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                <Star className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-white font-semibold">{testimonial.name}</h3>
                <p className="text-gray-400 text-sm">{testimonial.company}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < testimonial.rating ? "text-yellow-500 fill-current" : "text-gray-600"
                    }`}
                  />
                ))}
              </div>
              <Badge className={getStatusColor(testimonial.status)}>
                {testimonial.status}
              </Badge>
            </div>
          </div>
          
          <p className="text-gray-300 mb-3">{testimonial.message}</p>
          
          <div className="flex items-center justify-between text-sm text-gray-400">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              {testimonial.date}
            </div>
            <span className="text-red">{testimonial.project}</span>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <AdminLayout title="Messages & Feedback">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-2xl font-bold text-white">Messages & Feedback</h2>
          <p className="text-gray-400">Manage contact messages and testimonials</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {messageStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="bg-dark-lighter border border-red/20 rounded-xl p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-green-400 text-sm font-medium">
                    {stat.change}
                  </span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Tabs and Search */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div className="flex gap-2">
              <Button
                onClick={() => setActiveTab("messages")}
                className={activeTab === "messages" ? "bg-red hover:bg-red/80" : "bg-dark-bg hover:bg-dark-bg/80"}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Messages
              </Button>
              <Button
                onClick={() => setActiveTab("testimonials")}
                className={activeTab === "testimonials" ? "bg-red hover:bg-red/80" : "bg-dark-bg hover:bg-dark-bg/80"}
              >
                <Star className="w-4 h-4 mr-2" />
                Testimonials
              </Button>
            </div>
            
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search messages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-dark-bg border-red/30 text-white"
              />
            </div>
          </div>

          {activeTab === "messages" ? renderMessages() : renderTestimonials()}
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminMessages;
