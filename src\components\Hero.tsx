import React, { useEffect, useRef, useState } from "react";
import { ArrowDown, Star, Sparkles } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

const Hero = () => {
  const typingTextRef = useRef<HTMLSpanElement>(null);
  const texts = ["Web Developer", "UI/UX Designer", "Freelancer"];
  let textIndex = 0;
  let charIndex = 0;
  let isDeleting = false;
  let typingSpeed = 100;

  // Simple state for mobile name and profession
  const [mobileNameText, setMobileNameText] = useState("");
  const [showNameCursor, setShowNameCursor] = useState(true);
  const [startProfessionAnimation, setStartProfessionAnimation] =
    useState(false);
  const [currentProfession, setCurrentProfession] = useState("Web Developer");

  const professions = [
    "Web Developer",
    "UI/UX Designer",
    "<PERSON>lancer",
    "Creative Designer",
  ];

  useEffect(() => {
    function typeText() {
      const currentText = texts[textIndex];

      if (typingTextRef.current) {
        if (isDeleting) {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex - 1
          );
          charIndex--;
          typingSpeed = 50; // Faster when deleting
        } else {
          typingTextRef.current.textContent = currentText.substring(
            0,
            charIndex + 1
          );
          charIndex++;
          typingSpeed = 150; // Normal typing speed
        }

        // If word is complete, pause then start deleting
        if (!isDeleting && charIndex === currentText.length) {
          isDeleting = true;
          typingSpeed = 1000; // Wait before deleting
        }
        // If deletion is complete, move to next word
        else if (isDeleting && charIndex === 0) {
          isDeleting = false;
          textIndex = (textIndex + 1) % texts.length;
          typingSpeed = 500; // Wait before typing new word
        }
      }

      setTimeout(typeText, typingSpeed);
    }

    typeText();
  }, []);

  // Enhanced mobile name typing animation with cursor control
  useEffect(() => {
    const name = "John Smith";
    let nameIndex = 0;

    const typeNameChar = () => {
      if (nameIndex <= name.length) {
        setMobileNameText(name.substring(0, nameIndex));
        nameIndex++;
        setTimeout(typeNameChar, 150);
      } else {
        // Name typing complete - hide cursor and start profession animation
        setTimeout(() => {
          setShowNameCursor(false);
          setTimeout(() => {
            setStartProfessionAnimation(true);
          }, 300);
        }, 500);
      }
    };

    typeNameChar();
  }, []);

  // Cycle through professions after animation starts
  useEffect(() => {
    if (startProfessionAnimation) {
      let professionIndex = 0;

      const cycleProfessions = () => {
        setCurrentProfession(professions[professionIndex]);
        professionIndex = (professionIndex + 1) % professions.length;
      };

      // Start immediately with first profession
      cycleProfessions();

      // Then cycle every 4 seconds
      const interval = setInterval(cycleProfessions, 4000);

      return () => clearInterval(interval);
    }
  }, [startProfessionAnimation]);

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center py-20 lg:py-0"
    >
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8 lg:gap-12">
          {/* Mobile Profile Section - Only visible on mobile */}
          <div className="lg:hidden flex-shrink-0 text-center hero-profile-image">
            {/* Enhanced Profile Image with Rotating Border */}
            <div className="relative mx-auto w-36 h-36 mb-5 mt-20 group">
              {/* Outer Rotating Gradient Ring */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red via-white to-red p-0.5 animate-spin-slower">
                <div className="w-full h-full rounded-full bg-dark-bg"></div>
              </div>

              {/* Main Image Container */}
              <div className="absolute inset-0.5 rounded-full overflow-hidden ring-2 ring-red/30 group-hover:ring-red/50 transition-all duration-500">
                <Avatar className="w-full h-full shadow-xl">
                  <AvatarImage
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&auto=format&q=80"
                    alt="John Smith"
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <AvatarFallback className="bg-red/20 text-red text-3xl font-bold">
                    JS
                  </AvatarFallback>
                </Avatar>

                {/* Image Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-red/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Online status indicator */}
                <div className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-dark-bg animate-pulse shadow-md z-10"></div>
              </div>

              {/* Floating Stars */}
              <Star className="absolute -top-2 -right-2 w-4 h-4 text-yellow-400 animate-pulse" />
              <Sparkles className="absolute -bottom-1 -left-2 w-3 h-3 text-purple-400 animate-pulse delay-1000" />
            </div>

            {/* Name and Profession with Enhanced Cursors */}
            <div className="space-y-2 mb-3">
              {/* Name with enhanced colored cursor */}
              <h1 className="text-xl md:text-2xl font-bold mobile-name-text leading-tight">
                {mobileNameText}
                {showNameCursor && (
                  <span className="enhanced-name-cursor"></span>
                )}
              </h1>

              {/* Profession with smooth CSS animation - starts after name */}
              <div className="h-6 flex items-center justify-center mb-1">
                {startProfessionAnimation && (
                  <div
                    key={currentProfession}
                    className="typing-text text-base md:text-lg font-medium mobile-profession-text"
                  >
                    <span>{currentProfession}</span>
                  </div>
                )}
              </div>

              {/* Decorative line */}
              <div className="w-14 h-0.5 bg-gradient-to-r from-red to-red/50 mx-auto mt-1"></div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 space-y-8 text-center lg:text-left">
            {/* Enhanced Welcome Text */}
            <div className="relative">
              <h3 className="welcome-text text-lg md:text-xl font-semibold tracking-[0.3em] mb-2">
                WELCOME TO MY WORLD
              </h3>
              <div className="welcome-underline"></div>
            </div>
            {/* Enhanced Main Heading */}
            <div className="relative">
              <h1 className="enhanced-heading text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                <span className="hero-greeting">Hi, I'm </span>
                <span className="hero-name">John Smith</span>
                <br />
                <span className="hero-article">a </span>
                <span
                  ref={typingTextRef}
                  className="hero-profession typing-text typing-cursor"
                >
                  Web Developer
                </span>
              </h1>
              {/* Decorative elements */}
              <div className="hero-glow-effect"></div>
              <div className="hero-particles"></div>
            </div>
            {/* Enhanced Description */}
            <div className="relative">
              <p className="hero-description text-lg md:text-xl text-gray-200 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                I'm a web designer and developer based in USA. I specialize in
                building{" "}
                <span className="highlight-text">
                  exceptional digital experiences
                </span>
                . Currently, I'm focused on building{" "}
                <span className="highlight-text">
                  accessible, human-centered products
                </span>
                .
              </p>
            </div>

            {/* Enhanced Button */}
            <div className="pt-6">
              <a href="#about" className="enhanced-btn group">
                <span className="btn-text">More About Me</span>
                <div className="btn-glow"></div>
                <div className="btn-particles"></div>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute bottom-10 left-0 w-full flex justify-center animate-bounce-slow">
        <a href="#about" className="text-gray-300 hover:text-red">
          <ArrowDown size={30} />
        </a>
      </div>
    </section>
  );
};

export default Hero;
