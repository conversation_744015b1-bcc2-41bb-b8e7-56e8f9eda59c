@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Screen reader only class for accessibility */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 346.8 77.2% 49.8%;
    --radius: 0.75rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Light Theme Variables */
  .light {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 346.8 77.2% 49.8%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 89%;
    --sidebar-ring: 346.8 77.2% 49.8%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 346.8 77.2% 49.8%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-dark-bg dark:bg-dark-bg text-foreground font-poppins overflow-x-hidden;
  }

  body.light {
    @apply bg-gradient-to-br from-white via-gray-50 to-white;
  }

  ::selection {
    @apply bg-red text-white;
  }
}

@layer components {
  .heading-title {
    @apply text-gray-300 text-4xl md:text-5xl lg:text-6xl font-bold mb-6;
  }

  .section-title {
    @apply text-xl font-medium relative inline-block pb-2 text-gray-300 before:absolute before:w-2/3 before:h-[2px] before:bg-red before:bottom-0 before:left-0;
  }

  .section {
    @apply py-16 md:py-20 px-4 md:px-8;
  }

  .card {
    @apply bg-dark-lighter rounded-xl p-6 transition-all duration-300 border border-transparent hover:border-red hover:shadow-lg hover:shadow-red/10;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-red hover:bg-red-light text-white py-3 px-8;
  }

  .btn-outline {
    @apply border border-gray-300/40 hover:border-red text-gray-300 hover:text-red py-3 px-8;
  }

  .nav-link {
    @apply block py-3 px-6 text-gray-300 hover:text-white hover:bg-red rounded-lg transition-all duration-300;
  }

  .nav-link-dash {
    @apply block py-3 px-6 hover:text-red rounded-lg transition-all duration-300 relative;
  }

  /* Enhanced Header Title */
  .enhanced-header-title {
    position: relative;
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(
      135deg,
      #ffffff 0%,
      #ff6b9d 30%,
      #ff014f 70%,
      #ffffff 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: headerGradientShift 4s ease-in-out infinite;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
  }

  .header-title-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 40px;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.15) 0%,
      transparent 70%
    );
    animation: headerGlow 3s ease-in-out infinite alternate;
    z-index: -1;
    border-radius: 20px;
  }

  .header-title-underline {
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent,
      #ff014f,
      #ff6b9d,
      #ff014f,
      transparent
    );
    border-radius: 1px;
    animation: headerUnderlinePulse 2s ease-in-out infinite;
  }

  /* Mobile Header Title */
  .enhanced-header-title-mobile {
    position: relative;
    font-size: 1.25rem;
    font-weight: 600;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b9d 50%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: headerGradientShift 4s ease-in-out infinite;
    letter-spacing: 0.3px;
  }

  .header-title-underline-mobile {
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1.5px;
    background: linear-gradient(90deg, transparent, #ff014f, transparent);
    border-radius: 1px;
    animation: headerUnderlinePulse 2s ease-in-out infinite;
  }

  .active-nav-link {
    @apply bg-red text-white;
  }

  .progress-bar {
    @apply h-1 bg-red-light rounded-full;
  }

  .social-icon {
    @apply w-12 h-12 flex items-center justify-center rounded-lg bg-dark-lighter text-gray-300 hover:text-red hover:bg-transparent border border-transparent hover:border-red transition-all duration-300;
  }

  /* Enhanced Sidebar Styles */
  .enhanced-social-icon {
    @apply w-12 h-12 flex items-center justify-center rounded-xl bg-gradient-to-br from-dark-lighter to-dark-bg text-gray-300 hover:text-white border border-gray-700/30 hover:border-red/40 transition-all duration-500 transform hover:scale-110 hover:rotate-3 hover:shadow-lg hover:shadow-red/20;
  }

  .enhanced-contact-card {
    @apply bg-gradient-to-br from-dark-lighter/80 to-dark-bg/60 rounded-xl border border-gray-700/30 hover:border-gray-600/50 transition-all duration-500 backdrop-blur-sm hover:shadow-lg hover:shadow-black/20 transform hover:scale-105;
  }

  /* Enhanced Sidebar Scrollbar */
  .lg\:overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }

  .lg\:overflow-y-auto::-webkit-scrollbar-track {
    background: rgba(255, 1, 79, 0.05);
    border-radius: 3px;
  }

  .lg\:overflow-y-auto::-webkit-scrollbar-thumb {
    background: linear-gradient(
      to bottom,
      rgba(255, 1, 79, 0.3),
      rgba(255, 1, 79, 0.6)
    );
    border-radius: 3px;
    transition: background 0.3s ease;
  }

  .lg\:overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      to bottom,
      rgba(255, 1, 79, 0.6),
      rgba(255, 1, 79, 0.8)
    );
  }

  /* Animation Classes */
  .animate-spin-slow {
    animation: spin 8s linear infinite;
  }

  .animate-spin-slower {
    animation: spin 15s linear infinite;
  }

  .animate-gradient-x {
    animation: gradient-x 3s ease infinite;
    background-size: 300% 300%;
  }

  .bg-300% {
    background-size: 300% 300%;
  }

  @keyframes gradient-x {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Galaxy Animation Styles */
  .galaxy-container {
    position: absolute;
    width: 100%;
    height: 100%;
    animation: galaxy-rotate 60s linear infinite;
  }

  .galaxy-star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.8) 0%,
      transparent 70%
    );
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite alternate;
  }

  .galaxy-spiral {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%);
    animation: galaxy-spiral-rotate 40s linear infinite;
  }

  .spiral-arm {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 100%
    );
    transform-origin: 0 50%;
    border-radius: 1px;
  }

  .spiral-arm-1 {
    transform: translate(0, -50%) rotate(0deg);
    animation: spiral-arm-1 30s linear infinite;
  }

  .spiral-arm-2 {
    transform: translate(0, -50%) rotate(120deg);
    animation: spiral-arm-2 30s linear infinite;
  }

  .spiral-arm-3 {
    transform: translate(0, -50%) rotate(240deg);
    animation: spiral-arm-3 30s linear infinite;
  }

  .cosmic-particles {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .cosmic-particle {
    position: absolute;
    width: 1px;
    height: 1px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: float-cosmic 8s ease-in-out infinite;
  }

  @keyframes galaxy-rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes galaxy-spiral-rotate {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  @keyframes spiral-arm-1 {
    0% {
      transform: translate(0, -50%) rotate(0deg);
      opacity: 0.3;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      transform: translate(0, -50%) rotate(360deg);
      opacity: 0.3;
    }
  }

  @keyframes spiral-arm-2 {
    0% {
      transform: translate(0, -50%) rotate(120deg);
      opacity: 0.3;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      transform: translate(0, -50%) rotate(480deg);
      opacity: 0.3;
    }
  }

  @keyframes spiral-arm-3 {
    0% {
      transform: translate(0, -50%) rotate(240deg);
      opacity: 0.3;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      transform: translate(0, -50%) rotate(600deg);
      opacity: 0.3;
    }
  }

  @keyframes twinkle {
    0% {
      opacity: 0.3;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  @keyframes float-cosmic {
    0%,
    100% {
      transform: translateY(0px) translateX(0px);
      opacity: 0.3;
    }
    25% {
      transform: translateY(-20px) translateX(10px);
      opacity: 0.8;
    }
    50% {
      transform: translateY(-10px) translateX(-15px);
      opacity: 1;
    }
    75% {
      transform: translateY(-30px) translateX(5px);
      opacity: 0.6;
    }
  }

  .portfolio-filter-btn {
    @apply btn py-2 px-4 text-gray-300 hover:text-white bg-dark-lighter hover:bg-red rounded-lg;
  }

  /* Enhanced Portfolio Styles */
  .enhanced-portfolio-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: linear-gradient(135deg, #ffffff 0%, #ff014f 50%, #ffffff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    position: relative;
    animation: portfolioTitleGlow 3s ease-in-out infinite alternate;
  }

  .portfolio-title-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.15) 0%,
      transparent 70%
    );
    border-radius: 50%;
    animation: portfolioTitlePulse 4s ease-in-out infinite alternate;
    z-index: -1;
  }

  .portfolio-title-underline {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, transparent, #ff014f, transparent);
    border-radius: 2px;
    animation: portfolioUnderlineGlow 2s ease-in-out infinite alternate;
  }

  .portfolio-description-container {
    margin-top: 2rem;
    position: relative;
  }

  .portfolio-description {
    font-size: 1.125rem;
    line-height: 1.8;
    color: #a1a1aa;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }

  /* Mobile Portfolio Section Adjustments */
  @media (max-width: 768px) {
    .enhanced-portfolio-title {
      font-size: clamp(2rem, 8vw, 3rem);
      margin-bottom: 1rem;
    }

    .portfolio-description {
      font-size: 1rem;
      line-height: 1.6;
      padding: 0 1rem;
    }

    .portfolio-description-container {
      margin-top: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .enhanced-portfolio-title {
      font-size: clamp(1.75rem, 10vw, 2.5rem);
    }

    .portfolio-description {
      font-size: 0.9rem;
      padding: 0 0.5rem;
    }
  }

  /* Enhanced Filter Buttons */
  .enhanced-filter-btn {
    position: relative;
    padding: 12px 24px;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 1, 79, 0.05) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.2);
    border-radius: 50px;
    color: #a1a1aa;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    cursor: pointer;
    animation: filterBtnFadeIn 0.8s ease-out;
  }

  .enhanced-filter-btn:hover {
    transform: translateY(-2px) scale(1.05);
    border-color: rgba(255, 1, 79, 0.5);
    color: #ffffff;
    box-shadow: 0 10px 30px rgba(255, 1, 79, 0.2);
  }

  .enhanced-filter-btn.active {
    background: linear-gradient(135deg, #ff014f 0%, #ff4081 100%);
    border-color: #ff014f;
    color: #ffffff;
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 1, 79, 0.3);
  }

  .filter-btn-text {
    position: relative;
    z-index: 2;
  }

  .filter-btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle,
      rgba(255, 1, 79, 0.3) 0%,
      transparent 70%
    );
    border-radius: 50px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .enhanced-filter-btn:hover .filter-btn-glow,
  .enhanced-filter-btn.active .filter-btn-glow {
    opacity: 1;
  }

  .filter-btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    border-radius: 50px;
  }

  .filter-btn-particles::before,
  .filter-btn-particles::after {
    content: "";
    position: absolute;
    width: 2px;
    height: 2px;
    background: #ff014f;
    border-radius: 50%;
    opacity: 0;
    animation: filterParticleFloat 3s ease-in-out infinite;
  }

  .enhanced-filter-btn:hover .filter-btn-particles::before,
  .enhanced-filter-btn.active .filter-btn-particles::before {
    opacity: 1;
    top: 20%;
    left: 20%;
    animation-delay: 0s;
  }

  .enhanced-filter-btn:hover .filter-btn-particles::after,
  .enhanced-filter-btn.active .filter-btn-particles::after {
    opacity: 1;
    top: 60%;
    right: 20%;
    animation-delay: 1s;
  }

  /* Mobile Filter Button Adjustments */
  @media (max-width: 768px) {
    .enhanced-filter-btn {
      padding: 10px 20px;
      font-size: 0.85rem;
      margin: 0.25rem;
    }
  }

  @media (max-width: 480px) {
    .enhanced-filter-btn {
      padding: 8px 16px;
      font-size: 0.8rem;
      margin: 0.2rem;
    }
  }

  /* Portfolio Grid */
  .portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    transition: all 0.5s ease;
    justify-items: center;
    width: 100%;
  }

  /* Mobile Portfolio Grid Adjustments */
  @media (max-width: 768px) {
    .portfolio-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 0 1rem;
      justify-items: center;
      align-items: center;
    }
  }

  @media (max-width: 480px) {
    .portfolio-grid {
      grid-template-columns: 1fr;
      gap: 1.25rem;
      padding: 0 0.5rem;
      justify-items: center;
    }
  }

  .portfolio-grid.animating {
    opacity: 0.7;
    transform: scale(0.98);
  }

  .enhanced-portfolio-item {
    animation: portfolioItemFadeIn 0.8s ease-out;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .portfolio-card {
    position: relative;
    height: 100%;
    width: 100%;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.03) 0%,
      rgba(255, 1, 79, 0.02) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.1);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  /* Mobile Portfolio Card Adjustments */
  @media (max-width: 768px) {
    .enhanced-portfolio-item {
      max-width: 100%;
      width: 100%;
    }

    .portfolio-card {
      max-width: 350px;
      margin: 0 auto;
    }
  }

  @media (max-width: 480px) {
    .enhanced-portfolio-item {
      max-width: 100%;
      width: 100%;
    }

    .portfolio-card {
      max-width: 320px;
      margin: 0 auto;
    }

    .portfolio-image-container {
      height: 240px;
    }
  }

  .portfolio-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(255, 1, 79, 0.3);
    box-shadow: 0 25px 50px rgba(255, 1, 79, 0.15);
  }

  .portfolio-image-container {
    position: relative;
    width: 100%;
    height: 280px;
    overflow: hidden;
  }

  .portfolio-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .portfolio-card:hover .portfolio-image {
    transform: scale(1.1);
    filter: brightness(0.7);
  }

  .portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(255, 1, 79, 0.3) 100%
    );
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .portfolio-card:hover .portfolio-overlay {
    opacity: 1;
  }

  .portfolio-content {
    text-align: center;
    transform: translateY(30px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .portfolio-card:hover .portfolio-content {
    transform: translateY(0);
  }

  .portfolio-year {
    display: inline-block;
    padding: 4px 12px;
    background: rgba(255, 1, 79, 0.2);
    border: 1px solid rgba(255, 1, 79, 0.3);
    border-radius: 20px;
    color: #ff014f;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
  }

  .portfolio-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }

  .portfolio-description {
    font-size: 0.9rem;
    color: #a1a1aa;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .portfolio-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .tech-badge {
    padding: 4px 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .tech-badge:hover {
    background: rgba(255, 1, 79, 0.2);
    border-color: rgba(255, 1, 79, 0.4);
    transform: scale(1.05);
  }

  .portfolio-category {
    color: #ff014f;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  /* Mobile Portfolio Content Adjustments */
  @media (max-width: 768px) {
    .portfolio-overlay {
      padding: 1.5rem;
    }

    .portfolio-title {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
    }

    .portfolio-description {
      font-size: 0.85rem;
      margin-bottom: 1rem;
    }

    .tech-badge {
      font-size: 0.65rem;
      padding: 3px 8px;
    }

    .portfolio-category {
      font-size: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .portfolio-overlay {
      padding: 1rem;
    }

    .portfolio-title {
      font-size: 1.1rem;
    }

    .portfolio-description {
      font-size: 0.8rem;
      margin-bottom: 0.75rem;
    }

    .portfolio-technologies {
      gap: 0.25rem;
      margin-bottom: 0.75rem;
    }

    .tech-badge {
      font-size: 0.6rem;
      padding: 2px 6px;
    }
  }

  .typing-text {
    @apply overflow-hidden border-r-4 border-red whitespace-nowrap animate-typing;
  }

  /* ===== ENHANCED TESTIMONIALS STYLES ===== */

  /* Background Effects */
  .testimonials-bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      ellipse at center,
      rgba(255, 1, 79, 0.03) 0%,
      rgba(255, 1, 79, 0.01) 50%,
      transparent 100%
    );
    animation: testimonialsBgPulse 8s ease-in-out infinite;
  }

  .testimonials-floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        2px 2px at 20px 30px,
        rgba(255, 1, 79, 0.3),
        transparent
      ),
      radial-gradient(
        2px 2px at 40px 70px,
        rgba(255, 255, 255, 0.1),
        transparent
      ),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 1, 79, 0.2), transparent),
      radial-gradient(
        1px 1px at 130px 80px,
        rgba(255, 255, 255, 0.05),
        transparent
      );
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: testimonialsParticlesFloat 20s linear infinite;
  }

  @keyframes testimonialsBgPulse {
    0%,
    100% {
      opacity: 0.5;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  @keyframes testimonialsParticlesFloat {
    0% {
      transform: translateY(0px) rotate(0deg);
    }
    100% {
      transform: translateY(-100px) rotate(360deg);
    }
  }

  /* Enhanced Header */
  .enhanced-testimonials-header {
    position: relative;
    margin-bottom: 3rem;
  }

  .enhanced-testimonials-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
  }

  .testimonials-title-text {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: linear-gradient(135deg, #ffffff 0%, #ff014f 50%, #ffffff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: testimonialsGradientShift 4s ease-in-out infinite;
    position: relative;
    z-index: 2;
  }

  .testimonials-title-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.3) 0%,
      rgba(255, 1, 79, 0.1) 50%,
      transparent 100%
    );
    filter: blur(20px);
    animation: testimonialsGlowPulse 3s ease-in-out infinite;
    z-index: 1;
  }

  .testimonials-description-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
  }

  .testimonials-description {
    font-size: 1.125rem;
    line-height: 1.8;
    color: #a1a1aa;
    position: relative;
    z-index: 2;
  }

  .testimonials-description-accent {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #ff014f, transparent);
    border-radius: 2px;
    animation: testimonialsAccentGlow 2s ease-in-out infinite;
  }

  @keyframes testimonialsGradientShift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes testimonialsGlowPulse {
    0%,
    100% {
      opacity: 0.5;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  @keyframes testimonialsAccentGlow {
    0%,
    100% {
      opacity: 0.6;
      box-shadow: 0 0 10px rgba(255, 1, 79, 0.3);
    }
    50% {
      opacity: 1;
      box-shadow: 0 0 20px rgba(255, 1, 79, 0.6);
    }
  }

  /* Enhanced Testimonials Grid */
  .enhanced-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* Enhanced Testimonial Cards */
  .enhanced-testimonial-card {
    position: relative;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.02) 0%,
      rgba(255, 1, 79, 0.01) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.1);
    border-radius: 24px;
    padding: 2rem;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(50px) scale(0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .enhanced-testimonial-card.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .enhanced-testimonial-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(255, 1, 79, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 1, 79, 0.1);
  }

  /* Card Background Effects */
  .testimonial-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.02) 0%,
      rgba(255, 255, 255, 0.01) 50%,
      rgba(255, 1, 79, 0.02) 100%
    );
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: 1;
  }

  .enhanced-testimonial-card:hover .testimonial-card-bg {
    opacity: 1;
  }

  .testimonial-card-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.1) 0%,
      transparent 70%
    );
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: 1;
  }

  .enhanced-testimonial-card:hover .testimonial-card-glow {
    opacity: 1;
    animation: testimonialsCardGlow 2s ease-in-out infinite;
  }

  .testimonial-card-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        1px 1px at 20px 20px,
        rgba(255, 1, 79, 0.3),
        transparent
      ),
      radial-gradient(
        1px 1px at 80px 60px,
        rgba(255, 255, 255, 0.1),
        transparent
      ),
      radial-gradient(1px 1px at 120px 30px, rgba(255, 1, 79, 0.2), transparent);
    background-size: 140px 100px;
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: 1;
  }

  .enhanced-testimonial-card:hover .testimonial-card-particles {
    opacity: 1;
    animation: testimonialsCardParticles 15s linear infinite;
  }

  @keyframes testimonialsCardGlow {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
    }
    50% {
      transform: scale(1.1) rotate(180deg);
    }
  }

  @keyframes testimonialsCardParticles {
    0% {
      transform: translateY(0px);
    }
    100% {
      transform: translateY(-50px);
    }
  }

  /* Quote Icon */
  .testimonial-quote-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 3;
    opacity: 0.6;
    transition: all 0.3s ease;
  }

  .enhanced-testimonial-card:hover .testimonial-quote-icon {
    opacity: 1;
    transform: scale(1.1);
  }

  /* Enhanced Rating */
  .enhanced-rating-container {
    position: relative;
    margin-bottom: 1.5rem;
    z-index: 3;
  }

  .rating-stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
  }

  .star-wrapper {
    position: relative;
    transition: transform 0.3s ease;
  }

  .enhanced-star {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 0 4px rgba(255, 1, 79, 0.3));
  }

  .enhanced-star.filled {
    color: #ff014f;
    fill: #ff014f;
    animation: starGlow 2s ease-in-out infinite;
  }

  .enhanced-star.empty {
    color: #4a5568;
    fill: #4a5568;
  }

  .enhanced-testimonial-card:hover .star-wrapper {
    transform: scale(1.1);
  }

  .enhanced-testimonial-card:hover .enhanced-star.filled {
    animation: starPulse 1s ease-in-out infinite;
  }

  .rating-glow {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 1, 79, 0.5),
      transparent
    );
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .enhanced-testimonial-card:hover .rating-glow {
    opacity: 1;
    animation: ratingGlowSlide 2s ease-in-out infinite;
  }

  @keyframes starGlow {
    0%,
    100% {
      filter: drop-shadow(0 0 4px rgba(255, 1, 79, 0.3));
    }
    50% {
      filter: drop-shadow(0 0 8px rgba(255, 1, 79, 0.6));
    }
  }

  @keyframes starPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
  }

  @keyframes ratingGlowSlide {
    0%,
    100% {
      transform: translateY(-50%) translateX(-100%);
    }
    50% {
      transform: translateY(-50%) translateX(100%);
    }
  }

  /* Enhanced Text */
  .testimonial-text-container {
    position: relative;
    margin-bottom: 2rem;
    z-index: 3;
  }

  .enhanced-testimonial-text {
    font-size: 1rem;
    line-height: 1.7;
    color: #e2e8f0;
    font-style: italic;
    position: relative;
    padding: 0.5rem 0;
    min-height: 120px;
    display: flex;
    align-items: center;
  }

  .enhanced-testimonial-text::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: -10px;
    font-size: 3rem;
    color: rgba(255, 1, 79, 0.3);
    font-family: serif;
    line-height: 1;
  }

  .enhanced-testimonial-text::after {
    content: '"';
    position: absolute;
    bottom: -20px;
    right: -10px;
    font-size: 3rem;
    color: rgba(255, 1, 79, 0.3);
    font-family: serif;
    line-height: 1;
  }

  /* Enhanced Client Info */
  .enhanced-client-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 3;
  }

  .client-avatar-container {
    position: relative;
    flex-shrink: 0;
  }

  .client-avatar-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: radial-gradient(
      circle,
      rgba(255, 1, 79, 0.3) 0%,
      rgba(255, 1, 79, 0.1) 50%,
      transparent 100%
    );
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: avatarGlowPulse 3s ease-in-out infinite;
  }

  .enhanced-testimonial-card:hover .client-avatar-glow {
    opacity: 1;
  }

  .client-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 1, 79, 0.3);
    transition: all 0.3s ease;
    z-index: 2;
  }

  .enhanced-testimonial-card:hover .client-avatar {
    border-color: rgba(255, 1, 79, 0.6);
    transform: scale(1.05);
  }

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .enhanced-testimonial-card:hover .avatar-image {
    transform: scale(1.1);
  }

  .avatar-ring {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 2px solid transparent;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff014f, transparent, #ff014f) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: avatarRingRotate 4s linear infinite;
  }

  .enhanced-testimonial-card:hover .avatar-ring {
    opacity: 1;
  }

  .client-details {
    flex: 1;
    min-width: 0;
  }

  .client-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
  }

  .enhanced-testimonial-card:hover .client-name {
    color: #ff014f;
  }

  .client-position {
    font-size: 0.875rem;
    color: #ff014f;
    font-weight: 500;
    margin-bottom: 0.125rem;
  }

  .client-company {
    font-size: 0.75rem;
    color: #a1a1aa;
    margin-bottom: 0.75rem;
  }

  .project-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.1) 0%,
      rgba(255, 1, 79, 0.05) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.2);
    border-radius: 12px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    transition: all 0.3s ease;
  }

  .enhanced-testimonial-card:hover .project-badge {
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.2) 0%,
      rgba(255, 1, 79, 0.1) 100%
    );
    border-color: rgba(255, 1, 79, 0.4);
    transform: scale(1.05);
  }

  .project-name {
    color: #ffffff;
    font-weight: 500;
  }

  .project-duration {
    color: #a1a1aa;
  }

  .project-duration::before {
    content: "•";
    margin-right: 0.25rem;
    color: #ff014f;
  }

  /* Hover Overlay */
  .testimonial-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.02) 0%,
      rgba(255, 255, 255, 0.01) 50%,
      rgba(255, 1, 79, 0.02) 100%
    );
    opacity: 0;
    transition: opacity 0.6s ease;
    pointer-events: none;
    z-index: 2;
  }

  .enhanced-testimonial-card:hover .testimonial-hover-overlay {
    opacity: 1;
  }

  @keyframes avatarGlowPulse {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.3;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.1);
      opacity: 0.6;
    }
  }

  @keyframes avatarRingRotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Mobile Testimonials Responsiveness */
  @media (max-width: 768px) {
    .enhanced-testimonials-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 0 0.5rem;
    }

    .enhanced-testimonial-card {
      padding: 1.5rem;
      border-radius: 20px;
    }

    .testimonials-title-text {
      font-size: clamp(2rem, 8vw, 3rem);
    }

    .testimonials-description {
      font-size: 1rem;
      padding: 0 1rem;
    }

    .enhanced-testimonial-text {
      font-size: 0.9rem;
      min-height: 100px;
    }

    .enhanced-testimonial-text::before,
    .enhanced-testimonial-text::after {
      font-size: 2.5rem;
    }

    .client-avatar {
      width: 50px;
      height: 50px;
    }

    .client-avatar-glow {
      width: 70px;
      height: 70px;
    }

    .client-name {
      font-size: 1rem;
    }

    .client-position {
      font-size: 0.8rem;
    }

    .client-company {
      font-size: 0.7rem;
    }

    .project-badge {
      padding: 0.2rem 0.6rem;
      font-size: 0.7rem;
    }
  }

  @media (max-width: 480px) {
    .enhanced-testimonials-grid {
      gap: 1.25rem;
      padding: 0 0.25rem;
    }

    .enhanced-testimonial-card {
      padding: 1.25rem;
      border-radius: 16px;
    }

    .testimonials-title-text {
      font-size: clamp(1.75rem, 10vw, 2.5rem);
    }

    .testimonials-description {
      font-size: 0.9rem;
      padding: 0 0.5rem;
    }

    .enhanced-testimonial-text {
      font-size: 0.85rem;
      min-height: 80px;
    }

    .enhanced-testimonial-text::before,
    .enhanced-testimonial-text::after {
      font-size: 2rem;
    }

    .enhanced-client-info {
      gap: 0.75rem;
    }

    .client-avatar {
      width: 45px;
      height: 45px;
    }

    .client-avatar-glow {
      width: 65px;
      height: 65px;
    }

    .client-name {
      font-size: 0.95rem;
    }

    .client-position {
      font-size: 0.75rem;
    }

    .client-company {
      font-size: 0.65rem;
    }

    .project-badge {
      padding: 0.15rem 0.5rem;
      font-size: 0.65rem;
    }

    .enhanced-star {
      width: 16px;
      height: 16px;
    }

    .testimonial-quote-icon {
      top: 1rem;
      right: 1rem;
    }
  }

  .typing-cursor {
    @apply animate-blink-caret;
  }

  /* ===== ENHANCED CONTACT STYLES ===== */

  /* Background Effects */
  .contact-bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      ellipse at center,
      rgba(255, 1, 79, 0.04) 0%,
      rgba(59, 130, 246, 0.02) 50%,
      transparent 100%
    );
    animation: contactBgPulse 10s ease-in-out infinite;
  }

  .contact-floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        2px 2px at 25px 35px,
        rgba(255, 1, 79, 0.4),
        transparent
      ),
      radial-gradient(
        2px 2px at 45px 75px,
        rgba(59, 130, 246, 0.3),
        transparent
      ),
      radial-gradient(
        1px 1px at 95px 45px,
        rgba(16, 185, 129, 0.3),
        transparent
      ),
      radial-gradient(
        1px 1px at 135px 85px,
        rgba(255, 255, 255, 0.1),
        transparent
      );
    background-repeat: repeat;
    background-size: 160px 110px;
    animation: contactParticlesFloat 25s linear infinite;
  }

  @keyframes contactBgPulse {
    0%,
    100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.02);
    }
  }

  @keyframes contactParticlesFloat {
    0% {
      transform: translateY(0px) rotate(0deg);
    }
    100% {
      transform: translateY(-120px) rotate(360deg);
    }
  }

  /* Enhanced Header */
  .enhanced-contact-header {
    position: relative;
    margin-bottom: 3rem;
  }

  .enhanced-contact-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
  }

  .contact-title-text {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: linear-gradient(
      135deg,
      #ffffff 0%,
      #ff014f 30%,
      #3b82f6 70%,
      #ffffff 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 300% 300%;
    animation: contactGradientShift 6s ease-in-out infinite;
    position: relative;
    z-index: 2;
  }

  .contact-title-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 130%;
    height: 130%;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.4) 0%,
      rgba(59, 130, 246, 0.2) 50%,
      transparent 100%
    );
    filter: blur(25px);
    animation: contactGlowPulse 4s ease-in-out infinite;
    z-index: 1;
  }

  .contact-description-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
  }

  .contact-description {
    font-size: 1.125rem;
    line-height: 1.8;
    color: #a1a1aa;
    position: relative;
    z-index: 2;
  }

  .contact-description-accent {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(
      90deg,
      transparent,
      #ff014f,
      #3b82f6,
      transparent
    );
    border-radius: 2px;
    animation: contactAccentGlow 3s ease-in-out infinite;
  }

  @keyframes contactGradientShift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes contactGlowPulse {
    0%,
    100% {
      opacity: 0.6;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.15);
    }
  }

  @keyframes contactAccentGlow {
    0%,
    100% {
      opacity: 0.7;
      box-shadow: 0 0 15px rgba(255, 1, 79, 0.4);
    }
    50% {
      opacity: 1;
      box-shadow: 0 0 25px rgba(59, 130, 246, 0.6);
    }
  }

  /* Enhanced Contact Cards */
  .enhanced-contact-card {
    position: relative;
    opacity: 0;
    transform: translateY(50px) scale(0.9);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .enhanced-contact-card.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .contact-card-link {
    display: block;
    position: relative;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.03) 0%,
      rgba(255, 1, 79, 0.02) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.15);
    border-radius: 16px;
    padding: 1.25rem;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .contact-card-link:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(255, 1, 79, 0.4);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 40px rgba(255, 1, 79, 0.15);
  }

  /* Card Background Effects */
  .contact-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.03) 0%,
      rgba(59, 130, 246, 0.02) 50%,
      rgba(16, 185, 129, 0.02) 100%
    );
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: 1;
  }

  .contact-card-link:hover .contact-card-bg {
    opacity: 1;
  }

  .contact-card-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.15) 0%,
      transparent 70%
    );
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: 1;
  }

  .contact-card-link:hover .contact-card-glow {
    opacity: 1;
    animation: contactCardGlow 3s ease-in-out infinite;
  }

  .contact-card-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        1px 1px at 25px 25px,
        rgba(255, 1, 79, 0.4),
        transparent
      ),
      radial-gradient(
        1px 1px at 75px 65px,
        rgba(59, 130, 246, 0.3),
        transparent
      ),
      radial-gradient(
        1px 1px at 125px 35px,
        rgba(16, 185, 129, 0.3),
        transparent
      );
    background-size: 150px 100px;
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: 1;
  }

  .contact-card-link:hover .contact-card-particles {
    opacity: 1;
    animation: contactCardParticles 20s linear infinite;
  }

  @keyframes contactCardGlow {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
    }
    50% {
      transform: scale(1.2) rotate(180deg);
    }
  }

  @keyframes contactCardParticles {
    0% {
      transform: translateY(0px);
    }
    100% {
      transform: translateY(-60px);
    }
  }

  /* Icon Container */
  .contact-icon-container {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.4s ease;
    z-index: 3;
  }

  .contact-card-link:hover .contact-icon-container {
    transform: scale(1.1) rotate(5deg);
  }

  .contact-icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.3) 0%,
      transparent 70%
    );
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: contactIconGlow 4s ease-in-out infinite;
  }

  .contact-card-link:hover .contact-icon-glow {
    opacity: 1;
  }

  .contact-icon {
    position: relative;
    color: white;
    z-index: 2;
    transition: transform 0.3s ease;
  }

  .contact-card-link:hover .contact-icon {
    transform: scale(1.1);
  }

  .contact-icon-ring {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 2px solid transparent;
    border-radius: 12px;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.3),
        transparent,
        rgba(255, 255, 255, 0.3)
      )
      border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: contactIconRing 6s linear infinite;
  }

  .contact-card-link:hover .contact-icon-ring {
    opacity: 1;
  }

  @keyframes contactIconGlow {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.3;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.2);
      opacity: 0.6;
    }
  }

  @keyframes contactIconRing {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Contact Card Content */
  .contact-card-content {
    position: relative;
    z-index: 3;
  }

  .contact-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.375rem;
    transition: color 0.3s ease;
  }

  .contact-card-link:hover .contact-card-title {
    color: #ff014f;
  }

  .contact-card-description {
    font-size: 0.8rem;
    color: #a1a1aa;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
  }

  .contact-card-link:hover .contact-card-description {
    color: #d1d5db;
  }

  .contact-card-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: #e2e8f0;
    transition: all 0.3s ease;
  }

  .contact-card-link:hover .contact-card-value {
    color: #ffffff;
    transform: translateX(5px);
  }

  /* Hover Overlay */
  .contact-card-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.03) 0%,
      rgba(59, 130, 246, 0.02) 50%,
      rgba(16, 185, 129, 0.02) 100%
    );
    opacity: 0;
    transition: opacity 0.6s ease;
    pointer-events: none;
    z-index: 2;
  }

  .contact-card-link:hover .contact-card-hover-overlay {
    opacity: 1;
  }

  /* Enhanced Contact Form */
  .enhanced-contact-form {
    position: relative;
    opacity: 0;
    transform: translateY(50px) scale(0.9);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .enhanced-contact-form.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .contact-form {
    position: relative;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.04) 0%,
      rgba(255, 1, 79, 0.02) 100%
    );
    border: 1px solid rgba(255, 1, 79, 0.15);
    border-radius: 24px;
    padding: 2.5rem;
    overflow: hidden;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  /* Form Background Effects */
  .form-bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.02) 0%,
      rgba(59, 130, 246, 0.01) 50%,
      rgba(16, 185, 129, 0.01) 100%
    );
    z-index: 1;
  }

  .form-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        1px 1px at 30px 40px,
        rgba(255, 1, 79, 0.3),
        transparent
      ),
      radial-gradient(
        1px 1px at 80px 80px,
        rgba(59, 130, 246, 0.2),
        transparent
      ),
      radial-gradient(
        1px 1px at 130px 50px,
        rgba(16, 185, 129, 0.2),
        transparent
      );
    background-size: 160px 120px;
    animation: formParticlesFloat 30s linear infinite;
    z-index: 1;
  }

  @keyframes formParticlesFloat {
    0% {
      transform: translateY(0px) rotate(0deg);
    }
    100% {
      transform: translateY(-80px) rotate(360deg);
    }
  }

  /* Form Header */
  .form-header {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
    z-index: 3;
  }

  .form-header-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.2),
      rgba(255, 1, 79, 0.1)
    );
    border: 1px solid rgba(255, 1, 79, 0.3);
    border-radius: 16px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
  }

  .form-header-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 25px rgba(255, 1, 79, 0.3);
  }

  .form-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff, #ff014f);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .form-subtitle {
    font-size: 1rem;
    color: #a1a1aa;
    line-height: 1.6;
  }

  /* Enhanced Form Fields */
  .enhanced-form-field {
    position: relative;
    z-index: 3;
  }

  .enhanced-form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #d1d5db;
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
  }

  .label-icon {
    color: #ff014f;
    transition: transform 0.3s ease;
  }

  .label-text {
    flex: 1;
  }

  .label-required {
    color: #ff014f;
    font-weight: 600;
  }

  .form-input-container {
    position: relative;
  }

  .enhanced-form-input,
  .enhanced-form-textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 1, 79, 0.2);
    border-radius: 12px;
    padding: 1rem 1.25rem;
    font-size: 0.95rem;
    color: #ffffff;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
  }

  .enhanced-form-input::placeholder,
  .enhanced-form-textarea::placeholder {
    color: #6b7280;
    transition: color 0.3s ease;
  }

  .enhanced-form-input:focus,
  .enhanced-form-textarea:focus {
    outline: none;
    border-color: rgba(255, 1, 79, 0.6);
    background: rgba(255, 255, 255, 0.04);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 1, 79, 0.2);
  }

  .enhanced-form-input:focus::placeholder,
  .enhanced-form-textarea:focus::placeholder {
    color: #9ca3af;
  }

  .enhanced-form-textarea {
    resize: none;
    min-height: 120px;
  }

  /* Input Glow Effect */
  .input-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 1, 79, 0.1) 0%,
      rgba(59, 130, 246, 0.05) 100%
    );
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
  }

  .enhanced-form-input.focused + .input-glow,
  .enhanced-form-textarea.focused + .input-glow {
    opacity: 1;
  }

  /* Input Border Animation */
  .input-border-animation {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(
      45deg,
      rgba(255, 1, 79, 0.6),
      rgba(59, 130, 246, 0.4),
      rgba(16, 185, 129, 0.4),
      rgba(255, 1, 79, 0.6)
    );
    background-size: 300% 300%;
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.4s ease;
    animation: inputBorderFlow 3s ease-in-out infinite;
    z-index: 0;
  }

  .enhanced-form-input.focused ~ .input-border-animation,
  .enhanced-form-textarea.focused ~ .input-border-animation {
    opacity: 1;
  }

  @keyframes inputBorderFlow {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Enhanced Submit Button */
  .form-submit-container {
    position: relative;
    z-index: 3;
  }

  .enhanced-submit-btn {
    position: relative;
    width: 100%;
    background: transparent;
    border: none;
    border-radius: 16px;
    padding: 1.25rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
  }

  .enhanced-submit-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 1, 79, 0.3);
  }

  .enhanced-submit-btn:active {
    transform: translateY(-1px) scale(1.01);
  }

  .enhanced-submit-btn.submitting {
    pointer-events: none;
  }

  /* Button Background */
  .btn-bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #ff014f 0%, #e11d48 50%, #ff014f 100%);
    background-size: 200% 200%;
    animation: btnGradientShift 3s ease-in-out infinite;
    z-index: 1;
  }

  .btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        1px 1px at 20px 20px,
        rgba(255, 255, 255, 0.3),
        transparent
      ),
      radial-gradient(
        1px 1px at 60px 40px,
        rgba(255, 255, 255, 0.2),
        transparent
      ),
      radial-gradient(
        1px 1px at 100px 30px,
        rgba(255, 255, 255, 0.3),
        transparent
      );
    background-size: 120px 80px;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: btnParticlesMove 15s linear infinite;
    z-index: 2;
  }

  .enhanced-submit-btn:hover .btn-particles {
    opacity: 1;
  }

  .btn-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    z-index: 3;
  }

  .btn-icon {
    transition: transform 0.3s ease;
  }

  .enhanced-submit-btn:hover .btn-icon {
    transform: translateX(3px);
  }

  .btn-text {
    transition: transform 0.3s ease;
  }

  .btn-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
      ellipse,
      rgba(255, 1, 79, 0.4) 0%,
      transparent 70%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: btnGlowPulse 2s ease-in-out infinite;
    z-index: 0;
  }

  .enhanced-submit-btn:hover .btn-glow {
    opacity: 1;
  }

  /* Loading Spinner */
  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes btnGradientShift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes btnParticlesMove {
    0% {
      transform: translateY(0px);
    }
    100% {
      transform: translateY(-40px);
    }
  }

  @keyframes btnGlowPulse {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.4;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.2);
      opacity: 0.8;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Mobile Contact Responsiveness */
  @media (max-width: 768px) {
    .contact-title-text {
      font-size: clamp(2rem, 8vw, 3rem);
    }

    .contact-description {
      font-size: 1rem;
      padding: 0 1rem;
    }

    /* Mobile Navigation Menu Style Contact Cards */
    .contact-card-link {
      padding: 0.75rem;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      border-radius: 12px;
      background: rgba(31, 41, 55, 0.3);
      border: 1px solid rgba(255, 1, 79, 0.1);
    }

    .contact-card-link:hover {
      transform: none;
      background: rgba(31, 41, 55, 0.5);
      border-color: rgba(255, 1, 79, 0.2);
      box-shadow: none;
    }

    .contact-icon-container {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      margin-bottom: 0;
      flex-shrink: 0;
    }

    .contact-card-content {
      flex: 1;
      min-width: 0;
    }

    .contact-card-title {
      font-size: 0.75rem;
      color: #9ca3af;
      margin-bottom: 0.125rem;
      font-weight: 400;
    }

    .contact-card-description {
      display: none;
    }

    .contact-card-value {
      font-size: 0.875rem;
      font-weight: 500;
      color: #e5e7eb;
      margin-bottom: 0;
    }

    .contact-card-link:hover .contact-card-title {
      color: #9ca3af;
    }

    .contact-card-link:hover .contact-card-value {
      color: #ffffff;
      transform: none;
    }

    /* Hide all the fancy effects on mobile */
    .contact-card-bg,
    .contact-card-glow,
    .contact-card-particles,
    .contact-icon-glow,
    .contact-icon-ring,
    .contact-card-hover-overlay {
      display: none;
    }

    .contact-form {
      padding: 2rem;
    }

    .form-title {
      font-size: 1.5rem;
    }

    .enhanced-form-input,
    .enhanced-form-textarea {
      padding: 0.875rem 1rem;
      font-size: 0.9rem;
    }

    .enhanced-submit-btn {
      padding: 1rem 1.5rem;
      font-size: 0.95rem;
    }
  }

  @media (max-width: 480px) {
    .contact-title-text {
      font-size: clamp(1.75rem, 10vw, 2.5rem);
    }

    .contact-description {
      font-size: 0.9rem;
      padding: 0 0.5rem;
    }

    /* Keep the same compact mobile navigation style */
    .contact-card-link {
      padding: 0.75rem;
      gap: 0.75rem;
    }

    .contact-icon-container {
      width: 32px;
      height: 32px;
      border-radius: 8px;
    }

    .contact-card-title {
      font-size: 0.75rem;
    }

    .contact-card-value {
      font-size: 0.875rem;
    }

    .contact-form {
      padding: 1.5rem;
    }

    .form-title {
      font-size: 1.25rem;
    }

    .enhanced-form-input,
    .enhanced-form-textarea {
      padding: 0.75rem 0.875rem;
      font-size: 0.85rem;
    }

    .enhanced-submit-btn {
      padding: 0.875rem 1.25rem;
      font-size: 0.9rem;
    }
  }
}

/* Animated navbar dash underline */
@keyframes navDash {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

.animate-nav-dash {
  animation: navDash 0.5s forwards;
}

/* Mobile menu slide animations */
@keyframes slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-left {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* Mobile menu item animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Glassmorphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Mobile menu animations */
.mobile-nav-item {
  animation: slideInUp 0.3s ease-out forwards;
}

/* Navigation item click feedback */
.nav-item-clicked {
  transform: scale(0.95);
  transition: transform 0.1s ease-out;
}

.mobile-nav-item:nth-child(1) {
  animation-delay: 0.1s;
}
.mobile-nav-item:nth-child(2) {
  animation-delay: 0.15s;
}
.mobile-nav-item:nth-child(3) {
  animation-delay: 0.2s;
}
.mobile-nav-item:nth-child(4) {
  animation-delay: 0.25s;
}
.mobile-nav-item:nth-child(5) {
  animation-delay: 0.3s;
}
.mobile-nav-item:nth-child(6) {
  animation-delay: 0.35s;
}

/* Hamburger menu animation improvements */
.hamburger-line {
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced mobile menu backdrop */
.mobile-menu-backdrop {
  backdrop-filter: blur(8px);
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

/* Mobile menu content slide animation */
.mobile-menu-content {
  animation: slideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Social links hover effect */
.social-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-link:hover {
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.3);
}

/* Active navigation item glow effect */
.nav-item-active {
  box-shadow: 0 0 20px rgba(255, 1, 79, 0.3), 0 0 40px rgba(255, 1, 79, 0.1);
}

/* Mobile menu profile section animation */
.profile-section {
  animation: fadeInScale 0.6s ease-out 0.2s both;
}

/* Stunning Header Navigation Styles */
.stunning-header-scrolled {
  background: linear-gradient(
    135deg,
    rgba(30, 32, 36, 0.98) 0%,
    rgba(45, 48, 55, 0.95) 15%,
    rgba(30, 32, 36, 0.98) 30%,
    rgba(45, 48, 55, 0.95) 45%,
    rgba(30, 32, 36, 0.98) 60%,
    rgba(45, 48, 55, 0.95) 75%,
    rgba(30, 32, 36, 0.98) 100%
  );
  border-bottom: 1px solid rgba(255, 1, 79, 0.2);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4), 0 6px 20px rgba(255, 1, 79, 0.15),
    0 2px 8px rgba(255, 1, 79, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(255, 1, 79, 0.1);
  position: fixed !important;
  top: 0 !important;
  z-index: 100 !important;
}

.stunning-header-transparent {
  background: linear-gradient(
    135deg,
    rgba(30, 32, 36, 0.15) 0%,
    rgba(45, 48, 55, 0.08) 25%,
    rgba(30, 32, 36, 0.15) 50%,
    rgba(45, 48, 55, 0.08) 75%,
    rgba(30, 32, 36, 0.15) 100%
  );
  border-bottom: 1px solid rgba(255, 1, 79, 0.05);
  position: fixed !important;
  top: 0 !important;
  z-index: 100 !important;
}

/* Header Pattern Effect */
.header-pattern {
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(255, 1, 79, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 107, 157, 0.08) 0%,
      transparent 50%
    ),
    linear-gradient(
      45deg,
      transparent 40%,
      rgba(255, 1, 79, 0.02) 50%,
      transparent 60%
    );
  background-size: 100px 100px, 150px 150px, 200px 200px;
  animation: headerPatternMove 20s linear infinite;
}

@keyframes headerPatternMove {
  0% {
    background-position: 0% 0%, 0% 0%, 0% 0%;
  }
  100% {
    background-position: 100% 100%, -100% -100%, 50% 50%;
  }
}

/* Enhanced Header Glow Effects */
.stunning-header-scrolled::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 1, 79, 0.3) 20%,
    rgba(255, 1, 79, 0.6) 50%,
    rgba(255, 1, 79, 0.3) 80%,
    transparent 100%
  );
  animation: headerTopGlow 3s ease-in-out infinite alternate;
}

@keyframes headerTopGlow {
  0% {
    opacity: 0.3;
    transform: scaleX(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* Mobile Header Enhancements */
@media (max-width: 768px) {
  .stunning-header-scrolled {
    background: linear-gradient(
      135deg,
      rgba(30, 32, 36, 0.98) 0%,
      rgba(45, 48, 55, 0.95) 50%,
      rgba(30, 32, 36, 0.98) 100%
    );
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4), 0 2px 10px rgba(255, 1, 79, 0.15);
  }

  .header-pattern {
    background-size: 60px 60px, 90px 90px, 120px 120px;
    animation-duration: 15s;
  }
}

@media (max-width: 480px) {
  .stunning-header-scrolled {
    background: linear-gradient(
      135deg,
      rgba(30, 32, 36, 0.99) 0%,
      rgba(45, 48, 55, 0.97) 50%,
      rgba(30, 32, 36, 0.99) 100%
    );
  }

  .header-pattern {
    background-size: 40px 40px, 60px 60px, 80px 80px;
    opacity: 0.5;
  }
}

/* Header Floating Particles */
.header-particles {
  pointer-events: none;
}

.header-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.8) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: headerParticleFloat 8s ease-in-out infinite;
}

.header-particle-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
  animation-duration: 6s;
}

.header-particle-2 {
  top: 60%;
  left: 70%;
  animation-delay: 2s;
  animation-duration: 8s;
}

.header-particle-3 {
  top: 40%;
  left: 90%;
  animation-delay: 4s;
  animation-duration: 7s;
}

@keyframes headerParticleFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-10px) translateX(5px) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-5px) translateX(-3px) scale(0.8);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-15px) translateX(8px) scale(1.1);
    opacity: 0.9;
  }
}

/* Enhanced Hamburger Button */
.stunning-hamburger-btn {
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.stunning-hamburger-btn:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 12px rgba(255, 1, 79, 0.3));
}

.stunning-hamburger-btn:active {
  transform: scale(0.95);
  transition: transform 0.15s ease-out;
}

.enhanced-hamburger-line {
  position: relative;
  transform-origin: center;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.stunning-hamburger-btn:hover .enhanced-hamburger-line {
  transform: scaleX(1.05);
}

/* Mobile Particles Optimization */
@media (max-width: 768px) {
  .header-particle {
    width: 3px;
    height: 3px;
    animation-duration: 5s;
  }

  .header-particle-1 {
    animation-duration: 4s;
  }

  .header-particle-2 {
    animation-duration: 6s;
  }

  .header-particle-3 {
    animation-duration: 5s;
  }
}

@media (max-width: 480px) {
  .header-particle {
    width: 2px;
    height: 2px;
    opacity: 0.6;
  }
}

/* Enhanced Footer Styling */
.footer-content-wrapper {
  position: relative;
}

.footer-text {
  transition: all 0.3s ease;
  position: relative;
}

.footer-name {
  position: relative;
  transition: all 0.3s ease;
}

.footer-name::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, #ff014f, transparent);
  transition: width 0.3s ease;
}

.footer-name:hover::after {
  width: 100%;
}

.footer-mobile-extra {
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

/* Mobile Footer Responsiveness */
@media (max-width: 768px) {
  .footer-text {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .footer-name {
    display: inline-block;
    padding: 2px 4px;
    border-radius: 4px;
    background: rgba(255, 1, 79, 0.1);
  }

  .footer-mobile-extra {
    margin-top: 0.75rem;
    opacity: 0.7;
  }

  .footer-mobile-extra p {
    font-size: 0.75rem;
    font-style: italic;
  }
}

@media (max-width: 480px) {
  .footer-text {
    font-size: 0.8rem;
    padding: 0 0.5rem;
  }

  .footer-name {
    padding: 1px 3px;
    font-size: 0.8rem;
  }

  .footer-mobile-extra {
    margin-top: 0.5rem;
  }

  .footer-mobile-extra p {
    font-size: 0.7rem;
  }
}

/* Enhanced mobile responsiveness */
@media (max-width: 320px) {
  .mobile-menu-content {
    width: 100vw;
  }

  .footer-text {
    font-size: 0.75rem;
    padding: 0 0.25rem;
  }

  .footer-name {
    font-size: 0.75rem;
  }

  .footer-mobile-extra p {
    font-size: 0.65rem;
  }
}

/* Enhanced mobile menu scrollbar styling */
.mobile-menu-scrollable::-webkit-scrollbar {
  width: 2px;
}

.mobile-menu-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-menu-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 1, 79, 0.2);
  border-radius: 1px;
}

.mobile-menu-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 1, 79, 0.4);
}

/* Hide scrollbar on mobile devices but keep functionality */
@media (max-width: 768px) {
  .mobile-menu-scrollable {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .mobile-menu-scrollable::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* Remove default drawer backgrounds */
[data-vaul-drawer] {
  background: transparent !important;
}

[data-vaul-overlay] {
  background: transparent !important;
}

/* Ensure full height coverage */
.mobile-menu-full-height {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
  min-height: 100vh;
  min-height: 100dvh;
}

/* Prevent body scroll when mobile menu is open */
.mobile-menu-open {
  overflow: hidden;
}

/* Enhanced mobile menu positioning */
.mobile-menu-content {
  top: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  right: auto !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

/* Ensure header always stays fixed */
header[class*="fixed"] {
  position: fixed !important;
  top: 0 !important;
  z-index: 100 !important;
}

/* Mobile navigation improvements */
.nav-item-clicked {
  transform: scale(0.95);
  opacity: 0.7;
  transition: all 0.15s ease;
}

/* Enhanced Mobile Profile Section Styles */
.mobile-profile-container {
  position: relative;
  overflow: hidden;
}

.mobile-profile-container:hover .mobile-profile-glow {
  opacity: 1;
  animation: mobile-profile-pulse 2s ease-in-out infinite;
}

.mobile-profile-avatar {
  position: relative;
  z-index: 10;
}

.mobile-avatar-image {
  position: relative;
  z-index: 20;
}

.mobile-avatar-image:hover {
  transform: scale(1.1) rotate(2deg);
}

.mobile-profile-name {
  position: relative;
  z-index: 10;
}

.mobile-profile-title {
  position: relative;
  z-index: 10;
  background: linear-gradient(135deg, #ff014f, #ff6b9d);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mobile-profile-subtitle {
  position: relative;
  z-index: 10;
}

/* Mobile Profile Animations */
@keyframes mobile-profile-pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

@keyframes mobile-avatar-float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

.mobile-profile-avatar {
  animation: mobile-avatar-float 3s ease-in-out infinite;
}

/* Enhanced Mobile Profile Responsiveness */
@media (max-width: 480px) {
  .mobile-profile-container {
    padding: 1rem;
  }

  .mobile-profile-avatar {
    width: 4.5rem;
    height: 4.5rem;
  }

  .mobile-profile-name {
    font-size: 1.125rem;
  }

  .mobile-profile-title {
    font-size: 0.875rem;
  }

  .mobile-profile-subtitle {
    font-size: 0.75rem;
  }
}

@media (max-width: 320px) {
  .mobile-profile-container {
    padding: 0.75rem;
  }

  .mobile-profile-avatar {
    width: 4rem;
    height: 4rem;
  }

  .mobile-profile-name {
    font-size: 1rem;
  }
}

/* Override default drawer styles */
.mobile-menu-content > div:first-child {
  display: none !important; /* Hide the drag handle */
}

/* Animated skill progress bars */
@keyframes progressWidth {
  0% {
    width: 0;
  }
  100% {
    width: var(--percentage);
  }
}

@keyframes countUp {
  0% {
    content: "0%";
  }
  100% {
    content: attr(data-percentage) "%";
  }
}

.animate-progress {
  animation: progressWidth 1.5s forwards;
}

.skill-item.animated .progress-bar {
  animation: progressWidth 1.5s forwards;
}

/* Counter animation for percentage */
.skill-item.animated .counter-number {
  animation: countNumber 1.5s forwards;
}

@keyframes countNumber {
  0% {
    content: "0%";
  }
  100% {
    content: attr(data-percentage) "%";
  }
}

/* Scroll bar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e2024;
}

::-webkit-scrollbar-thumb {
  background: #c4cfde;
  border-radius: 50px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ff014f;
}

/* Perfect mobile hero centering */
@media (max-width: 1024px) {
  #home {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }
}

@media (max-width: 768px) {
  #home {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .hero-profile-image {
    margin-bottom: 1rem;
  }
}

/* Perfect mobile centering for smaller screens */
@media (max-width: 640px) {
  #home {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .hero-profile-image .mx-auto {
    margin-bottom: 1.5rem;
  }
}

/* Enhanced mobile profile styling */
.hero-profile-image .relative::before {
  content: "";
  position: absolute;
  inset: -10px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff014f, transparent, #ff014f);
  opacity: 0.2;
  animation: rotate 4s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile typing animation enhancements */
.mobile-name-text {
  background: linear-gradient(135deg, #ffffff, #e5e5e5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mobile-profession-text {
  background: linear-gradient(135deg, #ff014f, #ff6b9d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(255, 1, 79, 0.3);
}

/* Modern Enhanced Typing Cursor */
.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1.1em;
  margin-left: 2px;
  position: relative;
  animation: modernBlink 1s infinite ease-in-out;
}

.typing-cursor::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #e5e5e5 50%, #ffffff 100%);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2);
  animation: cursorGlow 2s infinite alternate ease-in-out;
}

.typing-cursor::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -1px;
  width: 4px;
  height: calc(100% + 4px);
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: cursorAura 1.5s infinite ease-in-out;
}

@keyframes modernBlink {
  0%,
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
  51%,
  100% {
    opacity: 0;
    transform: scaleY(0.8);
  }
}

@keyframes cursorGlow {
  0% {
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
      0 0 20px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2);
    filter: brightness(1);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 255, 255, 1),
      0 0 25px rgba(255, 255, 255, 0.6), 0 0 35px rgba(255, 255, 255, 0.3);
    filter: brightness(1.2);
  }
}

@keyframes cursorAura {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

/* Enhanced cursor for name text */
.mobile-name-text .typing-cursor::before {
  background: linear-gradient(180deg, #ffffff 0%, #f0f0f0 50%, #ffffff 100%);
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.9),
    0 0 24px rgba(255, 255, 255, 0.5), 0 0 36px rgba(255, 255, 255, 0.2),
    inset 0 0 3px rgba(255, 255, 255, 0.8);
}

/* Enhanced cursor for profession text */
.mobile-profession-text .typing-cursor::before {
  background: linear-gradient(
    180deg,
    #ffffff 0%,
    #ffebf0 30%,
    #ffffff 70%,
    #f0f0f0 100%
  );
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.9), 0 0 24px rgba(255, 1, 79, 0.3),
    0 0 36px rgba(255, 255, 255, 0.2), inset 0 0 3px rgba(255, 255, 255, 0.8);
}

/* Enhanced Name Cursor */
.enhanced-name-cursor {
  display: inline-block;
  width: 3px;
  height: 1.1em;
  margin-left: 3px;
  position: relative;
  animation: ultraBlink 1.2s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
}

.enhanced-name-cursor::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    #ffffff 0%,
    #f8f9fa 25%,
    #e9ecef 50%,
    #f8f9fa 75%,
    #ffffff 100%
  );
  border-radius: 3px;
  box-shadow: 0 0 8px rgba(255, 255, 255, 1), 0 0 16px rgba(255, 255, 255, 0.8),
    0 0 24px rgba(255, 255, 255, 0.6), 0 0 32px rgba(255, 255, 255, 0.4),
    inset 0 0 4px rgba(255, 255, 255, 0.9), inset 0 1px 0 rgba(255, 255, 255, 1);
  animation: ultraGlow 2.5s infinite alternate ease-in-out;
}

.enhanced-name-cursor::after {
  content: "";
  position: absolute;
  top: -3px;
  left: -2px;
  width: 7px;
  height: calc(100% + 6px);
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.3) 30%,
    rgba(255, 255, 255, 0.1) 60%,
    transparent 80%
  );
  border-radius: 50%;
  animation: ultraAura 2s infinite ease-in-out;
}

@keyframes ultraBlink {
  0%,
  45% {
    opacity: 1;
    transform: scaleY(1) scaleX(1);
  }
  46%,
  100% {
    opacity: 0;
    transform: scaleY(0.9) scaleX(1.1);
  }
}

@keyframes ultraGlow {
  0% {
    box-shadow: 0 0 8px rgba(255, 255, 255, 1),
      0 0 16px rgba(255, 255, 255, 0.8), 0 0 24px rgba(255, 255, 255, 0.6),
      0 0 32px rgba(255, 255, 255, 0.4), inset 0 0 4px rgba(255, 255, 255, 0.9),
      inset 0 1px 0 rgba(255, 255, 255, 1);
    filter: brightness(1) saturate(1);
  }
  100% {
    box-shadow: 0 0 12px rgba(255, 255, 255, 1),
      0 0 20px rgba(255, 255, 255, 0.9), 0 0 28px rgba(255, 255, 255, 0.7),
      0 0 36px rgba(255, 255, 255, 0.5), inset 0 0 6px rgba(255, 255, 255, 1),
      inset 0 2px 0 rgba(255, 255, 255, 1);
    filter: brightness(1.4) saturate(1.1);
  }
}

@keyframes ultraAura {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  25% {
    opacity: 0.6;
    transform: scale(1.2) rotate(90deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.4) rotate(180deg);
  }
  75% {
    opacity: 0.6;
    transform: scale(1.2) rotate(270deg);
  }
}

/* Enhanced Hero Content Styling */

/* Welcome Text Enhancement */
.welcome-text {
  background: linear-gradient(135deg, #c4cfde 0%, #ffffff 50%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(196, 207, 222, 0.3);
  animation: welcomeGlow 3s ease-in-out infinite alternate;
  position: relative;
}

.welcome-underline {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ff014f, transparent);
  animation: underlineExpand 2s ease-in-out infinite alternate;
}

@media (min-width: 1024px) {
  .welcome-underline {
    left: 0;
    transform: translateX(0);
  }
}

/* Enhanced Main Heading */
.enhanced-heading {
  position: relative;
  z-index: 2;
}

.hero-greeting {
  color: #c4cfde;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-name {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(255, 1, 79, 0.4);
  animation: nameGlowPulse 2s ease-in-out infinite alternate;
  position: relative;
}

.hero-article {
  color: #c4cfde;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-profession {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s ease-out 0.6s both;
}

/* Hero Glow Effect */
.hero-glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: heroGlow 4s ease-in-out infinite alternate;
  z-index: -1;
}

/* Hero Particles */
.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.hero-particles::before,
.hero-particles::after {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ff014f;
  border-radius: 50%;
  animation: particleFloat 6s ease-in-out infinite;
}

.hero-particles::before {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.hero-particles::after {
  top: 60%;
  right: 15%;
  animation-delay: 3s;
}

/* Typing animation enhancement */
.typing-cursor.typing-active {
  animation: typingPulse 0.1s ease-out;
}

/* Enhanced Description */
.hero-description {
  animation: fadeInUp 1s ease-out 0.8s both;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.highlight-text {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  position: relative;
}

.highlight-text::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ff014f, transparent);
  animation: highlightGlow 2s ease-in-out infinite alternate;
}

/* Enhanced Button */
.enhanced-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16px 32px;
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 50px;
  text-decoration: none;
  color: white;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(255, 1, 79, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: fadeInUp 1s ease-out 1s both;
}

.enhanced-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-text {
  position: relative;
  z-index: 2;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%
  );
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-btn:hover .btn-glow {
  opacity: 1;
}

.btn-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: 50px;
  overflow: hidden;
}

.btn-particles::before,
.btn-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: btnParticleFloat 3s ease-in-out infinite;
}

.btn-particles::before {
  top: 30%;
  left: 20%;
  animation-delay: 0s;
}

.btn-particles::after {
  top: 70%;
  right: 25%;
  animation-delay: 1.5s;
}

/* Enhanced Hero Keyframe Animations */
@keyframes welcomeGlow {
  0% {
    text-shadow: 0 0 30px rgba(196, 207, 222, 0.3);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 40px rgba(196, 207, 222, 0.5);
    filter: brightness(1.2);
  }
}

@keyframes underlineExpand {
  0% {
    width: 40px;
    opacity: 0.6;
  }
  100% {
    width: 80px;
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes nameGlowPulse {
  0% {
    text-shadow: 0 0 40px rgba(255, 1, 79, 0.4);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 60px rgba(255, 1, 79, 0.6);
    filter: brightness(1.3);
  }
}

@keyframes heroGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
    opacity: 0.6;
  }
}

@keyframes highlightGlow {
  0% {
    opacity: 0.6;
    transform: scaleX(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleX(1.2);
  }
}

@keyframes btnParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) scale(1.5);
    opacity: 0.8;
  }
}

/* Enhanced About Section Styling */

/* Background Effects */
.about-bg-glow {
  position: absolute;
  top: 20%;
  left: 10%;
  width: 400px;
  height: 400px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.08) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: aboutBgFloat 8s ease-in-out infinite alternate;
  z-index: 1;
}

.about-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.about-particles::before,
.about-particles::after {
  content: "";
  position: absolute;
  width: 3px;
  height: 3px;
  background: #ff014f;
  border-radius: 50%;
  animation: aboutParticleFloat 10s ease-in-out infinite;
}

.about-particles::before {
  top: 30%;
  left: 20%;
  animation-delay: 0s;
}

.about-particles::after {
  top: 70%;
  right: 25%;
  animation-delay: 5s;
}

/* Enhanced Section Title */
.enhanced-section-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: titleFadeIn 1s ease-out;
}

.title-glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 80px;
  background: radial-gradient(
    ellipse,
    rgba(255, 1, 79, 0.15) 0%,
    transparent 70%
  );
  animation: titleGlow 3s ease-in-out infinite alternate;
  z-index: -1;
}

.title-underline-animated {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 2px;
  animation: underlineSlide 2s ease-out;
}

/* Enhanced Text Blocks */
.about-text-block {
  position: relative;
  padding: 20px 0;
  animation: textBlockFadeIn 1s ease-out;
}

.about-text-block:nth-child(1) {
  animation-delay: 0.3s;
}

.about-text-block:nth-child(2) {
  animation-delay: 0.6s;
}

.about-description {
  color: #c4cfde;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.8;
}

.highlight-keyword {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  position: relative;
}

.highlight-keyword::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ff014f, transparent);
  animation: keywordGlow 2s ease-in-out infinite alternate;
}

.highlight-number {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 1.1em;
}

.tech-highlight {
  background: linear-gradient(135deg, #c4cfde 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 4px;
  position: relative;
}

.tech-highlight::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 1, 79, 0.1);
  border-radius: 4px;
  z-index: -1;
}

/* Enhanced Info Items */
.info-item {
  position: relative;
  padding: 16px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: infoItemFadeIn 1s ease-out;
  overflow: hidden;
}

.info-item:nth-child(1) {
  animation-delay: 0.9s;
}
.info-item:nth-child(2) {
  animation-delay: 1.1s;
}
.info-item:nth-child(3) {
  animation-delay: 1.3s;
}
.info-item:nth-child(4) {
  animation-delay: 1.5s;
}

.info-item:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.15);
}

.info-label {
  color: #ff014f;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.info-value {
  color: #ffffff;
  font-weight: 500;
  font-size: 1rem;
}

.info-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.05) 0%,
    transparent 50%
  );
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.info-item:hover .info-glow {
  opacity: 1;
}

/* Portfolio Background Effects */
.portfolio-bg-glow {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
  height: 600px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.08) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: portfolioBgGlow 8s ease-in-out infinite alternate;
  z-index: -1;
}

.portfolio-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.portfolio-particles::before,
.portfolio-particles::after {
  content: "";
  position: absolute;
  width: 3px;
  height: 3px;
  background: #ff014f;
  border-radius: 50%;
  animation: portfolioParticleFloat 8s ease-in-out infinite;
}

.portfolio-particles::before {
  top: 15%;
  left: 15%;
  animation-delay: 0s;
}

.portfolio-particles::after {
  top: 70%;
  right: 20%;
  animation-delay: 4s;
}

.portfolio-grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(255, 1, 79, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 1, 79, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: portfolioGridMove 20s linear infinite;
  z-index: -1;
}

.portfolio-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(255, 1, 79, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.portfolio-card:hover .portfolio-glow {
  opacity: 1;
}

.portfolio-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.portfolio-card:hover .portfolio-particles::before,
.portfolio-card:hover .portfolio-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  animation: cardParticleFloat 2s ease-in-out infinite;
}

.portfolio-card:hover .portfolio-particles::before {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.portfolio-card:hover .portfolio-particles::after {
  bottom: 20%;
  right: 20%;
  animation-delay: 1s;
}

/* Enhanced Feature Cards */
.enhanced-feature-card {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 32px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.03) 0%,
    rgba(255, 1, 79, 0.02) 100%
  );
  border-radius: 20px;
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: featureCardFadeIn 1s ease-out;
  overflow: hidden;
}

.enhanced-feature-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 20px 40px rgba(255, 1, 79, 0.15);
}

.feature-icon-container {
  position: relative;
  flex-shrink: 0;
}

.feature-icon {
  font-size: 3rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.enhanced-feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.2) 0%,
    transparent 70%
  );
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: iconGlowPulse 2s ease-in-out infinite alternate;
}

.enhanced-feature-card:hover .icon-glow {
  opacity: 1;
}

.icon-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.icon-particles::before,
.icon-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  opacity: 0;
  animation: iconParticleFloat 3s ease-in-out infinite;
}

.enhanced-feature-card:hover .icon-particles::before,
.enhanced-feature-card:hover .icon-particles::after {
  opacity: 1;
}

.icon-particles::before {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.icon-particles::after {
  top: 80%;
  right: 20%;
  animation-delay: 1.5s;
}

.feature-content {
  flex: 1;
}

.feature-title {
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.feature-description {
  color: #c4cfde;
  line-height: 1.6;
  font-size: 0.95rem;
}

.card-glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.05) 0%,
    transparent 50%
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-feature-card:hover .card-glow-effect {
  opacity: 1;
}

.card-border-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 1, 79, 0.5),
    transparent
  );
  opacity: 0;
  animation: borderSweep 2s ease-in-out infinite;
}

.enhanced-feature-card:hover .card-border-animation {
  opacity: 1;
}

/* About Section Keyframe Animations */
@keyframes aboutBgFloat {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(50px, -30px) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes aboutParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-30px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-40px) rotate(270deg);
    opacity: 0.6;
  }
}

@keyframes titleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes underlineSlide {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 80px;
    opacity: 1;
  }
}

@keyframes textBlockFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes keywordGlow {
  0% {
    opacity: 0.6;
    transform: scaleX(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleX(1.2);
  }
}

@keyframes infoItemFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes featureCardFadeIn {
  0% {
    opacity: 0;
    transform: translateX(50px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes iconGlowPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.2;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0.4;
  }
}

@keyframes iconParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-15px) scale(1.5);
    opacity: 0.8;
  }
}

@keyframes borderSweep {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced Resume Section Styling */

/* Background Effects */
.resume-bg-glow {
  position: absolute;
  top: 15%;
  right: 10%;
  width: 500px;
  height: 500px;
  background: radial-gradient(
    circle,
    rgba(255, 1, 79, 0.06) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: resumeBgFloat 10s ease-in-out infinite alternate;
  z-index: 1;
}

.resume-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.resume-particles::before,
.resume-particles::after {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ff014f;
  border-radius: 50%;
  animation: resumeParticleFloat 12s ease-in-out infinite;
}

.resume-particles::before {
  top: 25%;
  left: 15%;
  animation-delay: 0s;
}

.resume-particles::after {
  top: 75%;
  right: 20%;
  animation-delay: 6s;
}

.resume-grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(255, 1, 79, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 1, 79, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  z-index: 1;
}

/* Enhanced Resume Title */
.enhanced-resume-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #ff6b9d 30%,
    #ff014f 70%,
    #ffffff 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: resumeTitleFadeIn 1s ease-out;
}

.resume-title-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 100px;
  background: radial-gradient(
    ellipse,
    rgba(255, 1, 79, 0.2) 0%,
    transparent 70%
  );
  animation: resumeTitleGlow 4s ease-in-out infinite alternate;
  z-index: -1;
}

.resume-title-underline {
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 4px;
  background: linear-gradient(
    90deg,
    transparent,
    #ff014f,
    #ff6b9d,
    #ff014f,
    transparent
  );
  border-radius: 2px;
  animation: resumeUnderlineExpand 2s ease-out;
}

/* Enhanced Description */
.resume-description-container {
  margin-top: 24px;
  animation: resumeDescFadeIn 1s ease-out 0.3s both;
}

.resume-description {
  color: #c4cfde;
  font-size: 1.125rem;
  line-height: 1.8;
  max-width: 2xl;
  margin: 0 auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Enhanced Section Subtitles */
.enhanced-section-subtitle {
  font-size: 2rem;
  font-weight: 600;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation: subtitleFadeIn 1s ease-out;
}

.subtitle-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 60px;
  background: radial-gradient(
    ellipse,
    rgba(255, 1, 79, 0.15) 0%,
    transparent 70%
  );
  animation: subtitleGlow 3s ease-in-out infinite alternate;
  z-index: -1;
}

.subtitle-line {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 2px;
  animation: subtitleLineSlide 1.5s ease-out;
}

/* Enhanced Timeline Items */
.timeline-container {
  position: relative;
}

.enhanced-timeline-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 1, 79, 0.03) 100%
  );
  border-radius: 16px;
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: timelineItemFadeIn 1s ease-out;
  overflow: hidden;
}

.enhanced-timeline-item:hover {
  transform: translateX(8px) scale(1.02);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 12px 30px rgba(255, 1, 79, 0.15);
}

.timeline-connector {
  position: absolute;
  left: 11px;
  top: 60px;
  bottom: -32px;
  width: 2px;
  background: linear-gradient(180deg, #ff014f 0%, rgba(255, 1, 79, 0.3) 100%);
}

.enhanced-timeline-item:last-child .timeline-connector {
  display: none;
}

.timeline-dot {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  border-radius: 50%;
  border: 3px solid rgba(255, 1, 79, 0.2);
  position: relative;
  animation: timelineDotPulse 2s ease-in-out infinite alternate;
}

.timeline-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #ffffff;
  border-radius: 50%;
  animation: dotInnerGlow 1.5s ease-in-out infinite alternate;
}

.timeline-content {
  flex: 1;
}

.period-badge {
  display: inline-block;
  padding: 6px 16px;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.15) 0%,
    rgba(255, 107, 157, 0.15) 100%
  );
  color: #ff6b9d;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
  border: 1px solid rgba(255, 1, 79, 0.2);
}

.timeline-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.timeline-institution {
  color: #ff014f;
  font-weight: 500;
  margin-bottom: 12px;
  font-size: 1rem;
}

.timeline-description {
  color: #c4cfde;
  line-height: 1.6;
  font-size: 0.95rem;
}

.timeline-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 1, 79, 0.05) 0%,
    transparent 50%
  );
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-timeline-item:hover .timeline-glow {
  opacity: 1;
}

.timeline-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.timeline-particles::before,
.timeline-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  opacity: 0;
  animation: timelineParticleFloat 4s ease-in-out infinite;
}

.enhanced-timeline-item:hover .timeline-particles::before,
.enhanced-timeline-item:hover .timeline-particles::after {
  opacity: 1;
}

.timeline-particles::before {
  top: 20%;
  right: 20%;
  animation-delay: 0s;
}

.timeline-particles::after {
  top: 70%;
  right: 40%;
  animation-delay: 2s;
}

/* Enhanced Skills */
.enhanced-skill-item {
  position: relative;
  padding: 20px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 1, 79, 0.02) 100%
  );
  border-radius: 12px;
  border: 1px solid rgba(255, 1, 79, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: skillItemFadeIn 1s ease-out;
  overflow: hidden;
}

.enhanced-skill-item:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 1, 79, 0.3);
  box-shadow: 0 8px 25px rgba(255, 1, 79, 0.15);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skill-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #c4cfde 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.skill-percentage {
  color: #ff014f;
  font-weight: 700;
  font-size: 1rem;
}

.skill-bar-container {
  position: relative;
  margin-bottom: 8px;
}

.skill-bar-bg {
  position: relative;
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.skill-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  border-radius: 4px;
  position: relative;
  width: 0%;
  transition: width 2.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 10px rgba(255, 1, 79, 0.5);
}

.skill-bar-fill::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: skillBarShimmer 2s ease-in-out infinite;
}

.skill-bar-glow {
  position: absolute;
  top: -2px;
  left: 0;
  width: 0%;
  height: 12px;
  background: linear-gradient(
    90deg,
    rgba(255, 1, 79, 0.6),
    rgba(255, 107, 157, 0.8),
    rgba(255, 1, 79, 0.6)
  );
  border-radius: 6px;
  opacity: 0;
  transition: width 2.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
  filter: blur(1px);
  animation: skillGlowPulse 1s ease-in-out infinite alternate;
}

.enhanced-skill-item:hover .skill-bar-glow {
  opacity: 1;
}

/* Enhanced Animation States */
.skill-animating {
  transform: scale(1.02);
  border-color: rgba(255, 1, 79, 0.4);
  box-shadow: 0 12px 30px rgba(255, 1, 79, 0.2);
  transition: all 0.3s ease;
}

.skill-bar-animating {
  animation: skillBarPulse 2.5s ease-in-out;
}

.skill-bar-animating::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: skillBarSweep 2.5s ease-in-out;
}

.skill-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.skill-particles::before,
.skill-particles::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ff014f;
  border-radius: 50%;
  opacity: 0;
  animation: skillParticleFloat 3s ease-in-out infinite;
}

.enhanced-skill-item:hover .skill-particles::before,
.enhanced-skill-item:hover .skill-particles::after {
  opacity: 1;
}

.skill-particles::before {
  top: 30%;
  right: 20%;
  animation-delay: 0s;
}

.skill-particles::after {
  top: 70%;
  right: 40%;
  animation-delay: 1.5s;
}

/* Resume Section Keyframe Animations */
@keyframes resumeBgFloat {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-30px, 40px) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes resumeParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-40px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-50px) rotate(270deg);
    opacity: 0.6;
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes resumeTitleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes resumeTitleGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0.7;
  }
}

@keyframes resumeUnderlineExpand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 120px;
    opacity: 1;
  }
}

@keyframes resumeDescFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes subtitleFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes subtitleGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes subtitleLineSlide {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 60px;
    opacity: 1;
  }
}

@keyframes timelineItemFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-40px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes timelineDotPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 1, 79, 0.4);
  }
  100% {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px rgba(255, 1, 79, 0);
  }
}

@keyframes dotInnerGlow {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes timelineParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-15px) scale(1.5);
    opacity: 0.8;
  }
}

@keyframes skillItemFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes skillBarShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes skillParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) scale(1.3);
    opacity: 0.8;
  }
}

@keyframes skillGlowPulse {
  0% {
    filter: blur(1px) brightness(1);
  }
  100% {
    filter: blur(2px) brightness(1.3);
  }
}

@keyframes skillBarPulse {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(255, 1, 79, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 1, 79, 0.8), 0 0 30px rgba(255, 1, 79, 0.4);
  }
}

@keyframes skillBarSweep {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes typingPulse {
  0% {
    transform: scaleY(1) scaleX(1);
  }
  50% {
    transform: scaleY(1.1) scaleX(1.5);
  }
  100% {
    transform: scaleY(1) scaleX(1);
  }
}

/* Portfolio Animations */
@keyframes portfolioTitleGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 1, 79, 0.3);
  }
  100% {
    text-shadow: 0 0 30px rgba(255, 1, 79, 0.6), 0 0 40px rgba(255, 1, 79, 0.4);
  }
}

@keyframes portfolioTitlePulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes portfolioUnderlineGlow {
  0% {
    box-shadow: 0 0 10px rgba(255, 1, 79, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 1, 79, 0.8), 0 0 30px rgba(255, 1, 79, 0.6);
  }
}

@keyframes filterBtnFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes filterParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes portfolioItemFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes portfolioBgGlow {
  0% {
    transform: translateX(-50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translateX(-50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes portfolioParticleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.5;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
    opacity: 1;
  }
}

@keyframes portfolioGridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes cardParticleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) scale(1.2);
    opacity: 1;
  }
}

/* Header Title Animations */
@keyframes headerGradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes headerGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

@keyframes headerUnderlinePulse {
  0% {
    opacity: 0.5;
    transform: translateX(-50%) scaleX(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scaleX(1.2);
  }
  100% {
    opacity: 0.5;
    transform: translateX(-50%) scaleX(1);
  }
}

/* ===== LIGHT THEME STYLES ===== */

/* Light Theme Body and Base Styles */
.light body {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 25%,
    #f1f5f9 50%,
    #e2e8f0 75%,
    #f8fafc 100%
  );
  color: #1e293b;
  transition: all 0.5s ease;
}

.light {
  /* Custom Light Theme Colors */
  --light-bg: #ffffff;
  --light-lighter: #f8fafc;
  --light-darker: #f1f5f9;
  --light-text: #1e293b;
  --light-text-secondary: #475569;
  --light-text-muted: #64748b;
  --light-border: #e2e8f0;
  --light-border-hover: #cbd5e1;
  --light-shadow: rgba(15, 23, 42, 0.08);
  --light-shadow-lg: rgba(15, 23, 42, 0.12);
}

/* Light Theme Sidebar */
.light .sidebar-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
  border-right: 1px solid #e2e8f0;
  box-shadow: 4px 0 20px rgba(15, 23, 42, 0.05);
}

.light .sidebar-profile-container {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
}

.light .sidebar-profile-name {
  color: #1e293b;
}

.light .sidebar-profile-title {
  color: #ff6b9d;
}

.light .sidebar-profile-subtitle {
  color: #64748b;
}

/* Light Theme Navigation */
.light .nav-item {
  color: #64748b;
  border: 1px solid transparent;
}

.light .nav-item:hover {
  color: #1e293b;
  background: rgba(255, 107, 157, 0.08);
  border-color: rgba(255, 107, 157, 0.2);
}

.light .nav-item.active {
  color: #ff014f;
  background: rgba(255, 107, 157, 0.12);
  border-color: rgba(255, 107, 157, 0.3);
}

/* Light Theme Header */
.light .stunning-header-scrolled {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(255, 255, 255, 0.95) 100%
  );
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08),
    0 2px 8px rgba(255, 107, 157, 0.05);
  backdrop-filter: blur(20px);
}

.light .enhanced-header-title {
  background: linear-gradient(
    135deg,
    #1e293b 0%,
    #ff6b9d 30%,
    #ff014f 70%,
    #1e293b 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .enhanced-header-title-mobile {
  background: linear-gradient(135deg, #1e293b 0%, #ff6b9d 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Light Theme Hero Section */
.light .hero-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
}

.light .hero-name {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .hero-profession {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .hero-article {
  color: #475569;
}

/* Light Theme Cards and Components */
.light .card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
}

.light .card:hover {
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 8px 30px rgba(255, 107, 157, 0.15);
}

.light .enhanced-feature-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
}

.light .enhanced-feature-card:hover {
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 12px 40px rgba(255, 107, 157, 0.15);
}

/* Light Theme Section Titles */
.light .enhanced-section-title {
  background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .enhanced-portfolio-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .testimonials-title-text {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .contact-title-text {
  background: linear-gradient(
    135deg,
    #1e293b 0%,
    #ff014f 30%,
    #3b82f6 70%,
    #1e293b 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Light Theme Footer */
.light footer {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f8fafc 100%);
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -4px 20px rgba(15, 23, 42, 0.05);
}

.light .footer-text {
  color: #334155;
}

.light .footer-name {
  color: #ff014f;
}

.light .footer-subtitle p {
  color: #475569;
}

.light .theme-toggle-wrapper {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
}

.light .theme-toggle-wrapper:hover {
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 8px 30px rgba(255, 107, 157, 0.15);
}

.light .theme-toggle-label {
  color: #475569;
}

/* Light Theme Forms */
.light .enhanced-form-field input,
.light .enhanced-form-field textarea {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  color: #1e293b;
}

.light .enhanced-form-field input:focus,
.light .enhanced-form-field textarea:focus {
  border-color: rgba(255, 107, 157, 0.5);
  box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.light .enhanced-form-label {
  color: #475569;
}

/* Light Theme Skills */
.light .enhanced-skill-item {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 20px rgba(15, 23, 42, 0.08);
}

.light .enhanced-skill-item:hover {
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 8px 30px rgba(255, 107, 157, 0.15);
}

/* Light Theme Text Colors */
.light .text-gray-300 {
  color: #334155 !important;
}

.light .text-gray-400 {
  color: #475569 !important;
}

.light .text-gray-500 {
  color: #475569 !important;
}

.light .text-white {
  color: #1e293b !important;
}

/* ===== COMPREHENSIVE LIGHT THEME STYLES ===== */

/* Light Theme Base Components */
.light .heading-title {
  color: #1e293b !important;
}

.light .section-title {
  color: #1e293b !important;
}

.light .section-title::before {
  background-color: #ff014f !important;
}

/* Light Theme Buttons */
.light .btn-primary {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  color: #ffffff;
  border: 1px solid transparent;
  box-shadow: 0 4px 15px rgba(255, 1, 79, 0.3);
}

.light .btn-primary:hover {
  background: linear-gradient(135deg, #e6013e 0%, #e55a8a 100%);
  box-shadow: 0 6px 20px rgba(255, 1, 79, 0.4);
  transform: translateY(-2px);
}

/* Light Theme Portfolio Section */
.light .enhanced-portfolio-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .enhanced-filter-btn {
  background: rgba(255, 255, 255, 0.8);
  color: #64748b;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(15, 23, 42, 0.08);
}

.light .enhanced-filter-btn:hover {
  background: rgba(255, 107, 157, 0.1);
  color: #ff014f;
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 4px 15px rgba(255, 107, 157, 0.15);
}

.light .enhanced-filter-btn.active {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  color: #ffffff;
  border-color: #ff014f;
  box-shadow: 0 4px 15px rgba(255, 1, 79, 0.3);
}

.light .portfolio-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .portfolio-card:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
}

.light .portfolio-content h3 {
  color: #1e293b;
}

.light .portfolio-content p {
  color: #475569;
}

.light .portfolio-tech-tag {
  background: rgba(255, 107, 157, 0.1);
  color: #ff014f;
  border: 1px solid rgba(255, 107, 157, 0.2);
}

/* Light Theme Skills Section */
.light .enhanced-skill-item {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .enhanced-skill-item:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 12px 35px rgba(255, 107, 157, 0.2);
}

.light .skill-name {
  color: #1e293b;
  font-weight: 600;
}

.light .skill-percentage {
  color: #ff014f;
  font-weight: 700;
}

.light .skill-bar-bg {
  background: rgba(226, 232, 240, 0.6);
  border: 1px solid rgba(203, 213, 225, 0.8);
}

.light .skill-bar-fill {
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  box-shadow: 0 2px 8px rgba(255, 1, 79, 0.3);
}

.light .skill-bar-glow {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 1, 79, 0.4) 50%,
    transparent 100%
  );
}

/* Light Theme Testimonials Section */
.light .testimonials-title-text {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .testimonials-description {
  color: #64748b;
}

.light .enhanced-testimonial-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .enhanced-testimonial-card:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
}

.light .enhanced-testimonial-text {
  color: #475569;
}

.light .enhanced-testimonial-text::before,
.light .enhanced-testimonial-text::after {
  color: rgba(255, 1, 79, 0.3);
}

.light .client-name {
  color: #1e293b;
}

.light .client-position {
  color: #ff014f;
}

.light .client-company {
  color: #64748b;
}

.light .testimonial-quote-icon {
  color: rgba(255, 1, 79, 0.6);
}

/* Light Theme Services Section */
.light .enhanced-service-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .enhanced-service-card:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
}

.light .service-icon {
  color: #ff014f;
}

.light .service-title {
  color: #1e293b;
}

.light .service-description {
  color: #334155;
}

/* ===== LIGHT THEME SECTION BACKGROUNDS ===== */

.light #about {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

.light #resume {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

.light #skills {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

.light #portfolio {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

.light #testimonials {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

.light #contact {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

.light .sidebar-container {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

.light footer {
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 50%,
    #ffffff 100%
  ) !important;
}

/* Light Theme Header Overrides */
.light header .bg-gradient-to-r {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.9),
    rgba(255, 255, 255, 0.95)
  ) !important;
}

.light .mobile-menu-content {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.95)
  ) !important;
  border-color: rgba(226, 232, 240, 0.5) !important;
}

/* Light Theme About Section */
.light .about-content h2 {
  color: #1e293b;
}

.light .about-content p {
  color: #334155;
}

.light .about-stats-item {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .about-stats-item:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 12px 35px rgba(255, 107, 157, 0.2);
}

.light .stats-number {
  color: #ff014f;
}

.light .stats-label {
  color: #475569;
}

/* Light Theme Contact Section */
.light .contact-title-text {
  background: linear-gradient(
    135deg,
    #1e293b 0%,
    #ff014f 30%,
    #3b82f6 70%,
    #1e293b 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .contact-description {
  color: #334155;
}

.light .enhanced-form-field {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 15px rgba(15, 23, 42, 0.05);
}

.light .enhanced-form-field:focus-within {
  border-color: rgba(255, 107, 157, 0.5);
  box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.light .enhanced-form-label {
  color: #334155;
}

.light .enhanced-form-field input,
.light .enhanced-form-field textarea {
  background: transparent;
  color: #1e293b;
}

.light .enhanced-form-field input::placeholder,
.light .enhanced-form-field textarea::placeholder {
  color: #94a3b8;
}

/* Light Theme Hero Section Enhancements */
.light .welcome-text {
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
  color: #1e293b !important;
  text-shadow: none;
  font-weight: 600;
}

.light .hero-greeting {
  color: #334155;
}

.light .hero-name {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.light .hero-profession {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .hero-article {
  color: #64748b;
}

.light .hero-description {
  color: #64748b;
}

/* Light Theme Profile Image */
.light .hero-profile-image .bg-dark-bg {
  background: #ffffff !important;
}

.light .hero-profile-image .border-dark-bg {
  border-color: #ffffff !important;
}

/* Light Theme Welcome Underline */
.light .welcome-underline::after {
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
}

/* Light Theme Enhanced Elements */
.light .enhanced-heading {
  color: #1e293b;
}

.light .enhanced-description {
  color: #64748b;
}

/* Light Theme Badges and Tags */
.light .badge {
  background: rgba(255, 107, 157, 0.1);
  color: #ff014f;
  border: 1px solid rgba(255, 107, 157, 0.2);
}

.light .tag {
  background: rgba(255, 107, 157, 0.1);
  color: #ff014f;
  border: 1px solid rgba(255, 107, 157, 0.2);
}

/* Light Theme Social Links */
.light .social-link {
  background: rgba(255, 255, 255, 0.8);
  color: #475569;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 15px rgba(15, 23, 42, 0.08);
}

.light .social-link:hover {
  background: rgba(255, 107, 157, 0.1);
  color: #ff014f;
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 6px 20px rgba(255, 107, 157, 0.15);
}

/* Light Theme Progress Bars */
.light .progress-bar {
  background: linear-gradient(90deg, #ff014f 0%, #ff6b9d 50%, #ff014f 100%);
}

.light .progress-bg {
  background: rgba(226, 232, 240, 0.6);
}

/* Light Theme Animations and Effects */
.light .glow-effect {
  box-shadow: 0 0 20px rgba(255, 1, 79, 0.3);
}

.light .shadow-effect {
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .border-effect {
  border: 1px solid rgba(255, 107, 157, 0.3);
}

/* Light Theme Mobile Specific */
@media (max-width: 768px) {
  .light .mobile-hero-bg {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
  }

  .light .mobile-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 4px 15px rgba(15, 23, 42, 0.08);
  }
}

/* Light Theme Additional Components */
.light .enhanced-feature-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .enhanced-feature-card:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
}

/* Light Theme Enhanced Text Visibility */
.light .enhanced-description {
  color: #334155;
}

.light p {
  color: #334155;
}

.light .text-muted {
  color: #475569 !important;
}

.light .text-secondary {
  color: #475569 !important;
}

.light .description {
  color: #334155;
}

.light .subtitle {
  color: #475569;
}

.light .label {
  color: #334155;
}

.light .caption {
  color: #475569;
}

/* Light Theme Tables */
.light table {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.light th {
  background: rgba(248, 250, 252, 0.8);
  color: #1e293b;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

.light td {
  color: #64748b;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.light tr:hover {
  background: rgba(255, 107, 157, 0.05);
}

/* Light Theme Modals and Overlays */
.light .modal {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 20px 50px rgba(15, 23, 42, 0.15);
}

.light .overlay {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

/* Light Theme Dropdown Menus */
.light .dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.1);
}

.light .dropdown-item {
  color: #64748b;
}

.light .dropdown-item:hover {
  background: rgba(255, 107, 157, 0.1);
  color: #ff014f;
}

/* Light Theme Tooltips */
.light .tooltip {
  background: rgba(30, 41, 59, 0.9);
  color: #ffffff;
  border: 1px solid rgba(71, 85, 105, 0.3);
}

/* Light Theme Scrollbars */
.light ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.light ::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.3);
}

.light ::-webkit-scrollbar-thumb {
  background: rgba(255, 107, 157, 0.5);
  border-radius: 4px;
}

.light ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 107, 157, 0.7);
}

/* Light Theme Selection */
.light ::selection {
  background: rgba(255, 107, 157, 0.3);
  color: #1e293b;
}

/* Light Theme Focus States */
.light *:focus {
  outline: 2px solid rgba(255, 107, 157, 0.5);
  outline-offset: 2px;
}

/* Light Theme Disabled States */
.light .disabled,
.light :disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Light Theme Loading States */
.light .loading {
  background: linear-gradient(
    90deg,
    rgba(226, 232, 240, 0.3) 25%,
    rgba(255, 107, 157, 0.1) 50%,
    rgba(226, 232, 240, 0.3) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== LIGHT THEME ABOUT SECTION ===== */

.light .enhanced-section-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .about-description {
  color: #334155;
}

.light .highlight-keyword {
  color: #ff014f;
  font-weight: 600;
}

.light .highlight-number {
  color: #ff014f;
  font-weight: 700;
}

.light .tech-highlight {
  color: #3b82f6;
  font-weight: 600;
}

.light .info-label {
  color: #334155;
  font-weight: 500;
}

.light .info-value {
  color: #1e293b;
  font-weight: 600;
}

.light .feature-title {
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
  color: #1e293b !important;
  font-weight: 600;
}

.light .feature-description {
  color: #334155;
}

.light .about-text-block {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(15, 23, 42, 0.05);
}

.light .info-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 15px rgba(15, 23, 42, 0.05);
  transition: all 0.3s ease;
}

.light .info-item:hover {
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.1);
  transform: translateY(-2px);
}

/* Light Theme Sidebar Contact Cards */
.light .enhanced-contact-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(15, 23, 42, 0.05);
  transition: all 0.3s ease;
}

.light .enhanced-contact-card:hover {
  border-color: rgba(255, 107, 157, 0.3);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.1);
  transform: translateY(-2px);
}

/* Light Theme Social Icons */
.light .enhanced-social-icon {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  color: #64748b;
  box-shadow: 0 4px 15px rgba(15, 23, 42, 0.08);
}

.light .enhanced-social-icon:hover {
  border-color: rgba(255, 107, 157, 0.3);
  color: #ff014f;
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.15);
}

/* ===== LIGHT THEME RESUME SECTION ===== */

.light .enhanced-resume-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
}

.light .resume-description {
  color: #334155;
  font-size: 1.125rem;
  line-height: 1.75;
  max-width: 600px;
  margin: 0 auto;
}

.light .highlight-text {
  color: #ff014f;
  font-weight: 600;
}

.light .enhanced-section-subtitle {
  color: #1e293b;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

.light .enhanced-timeline-item {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.light .enhanced-timeline-item:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
  transform: translateY(-4px);
}

.light .timeline-title {
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
  color: #1e293b !important;
  font-weight: 600;
}

.light .timeline-institution {
  color: #ff014f !important;
  font-weight: 500;
}

.light .timeline-description {
  color: #334155 !important;
}

.light .period-badge {
  background: rgba(255, 107, 157, 0.1);
  color: #ff014f;
  border: 1px solid rgba(255, 107, 157, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Light Theme Skills Section */
.light .skill-title {
  color: #1e293b !important;
  font-weight: 600;
}

.light .skill-description {
  color: #334155 !important;
}

.light .skill-level {
  color: #475569 !important;
  display: inline-block;
  margin-bottom: 1rem;
}

.light .timeline-dot {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  border: 3px solid white;
  box-shadow: 0 4px 15px rgba(255, 1, 79, 0.3);
}

.light .timeline-connector {
  background: linear-gradient(to bottom, #ff014f, rgba(255, 1, 79, 0.3));
}

/* ===== LIGHT THEME PORTFOLIO SECTION ===== */

.light .enhanced-portfolio-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .portfolio-description {
  color: #334155;
}

.light .portfolio-filter-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  color: #475569;
  transition: all 0.3s ease;
}

.light .portfolio-filter-btn.active,
.light .portfolio-filter-btn:hover {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  border-color: #ff014f;
  color: white;
  box-shadow: 0 4px 15px rgba(255, 1, 79, 0.3);
}

.light .portfolio-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .portfolio-card:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
}

.light .portfolio-title {
  color: #1e293b;
}

.light .portfolio-description {
  color: #64748b;
}

.light .portfolio-year {
  background: linear-gradient(135deg, #ff014f 0%, #ff6b9d 100%);
  color: white;
}

/* ===== LIGHT THEME SKILLS SECTION ===== */

.light .enhanced-skills-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .skills-description {
  color: #334155;
}

/* ===== LIGHT THEME TESTIMONIALS SECTION ===== */

.light .enhanced-testimonials-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .testimonials-description {
  color: #334155;
}

.light .enhanced-testimonial-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .enhanced-testimonial-card:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
}

.light .enhanced-testimonial-text {
  color: #64748b;
}

.light .client-name {
  color: #1e293b;
}

.light .client-position {
  color: #ff014f;
}

.light .client-company {
  color: #64748b;
}

/* ===== LIGHT THEME CONTACT SECTION ===== */

.light .enhanced-contact-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.light .contact-description {
  color: #64748b;
}

.light .enhanced-contact-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .enhanced-contact-card:hover {
  border-color: rgba(255, 107, 157, 0.4);
  box-shadow: 0 15px 40px rgba(255, 107, 157, 0.2);
}

.light .contact-card-title {
  color: #1e293b;
}

.light .contact-card-description {
  color: #64748b;
}

.light .contact-card-value {
  color: #ff014f;
}

.light .enhanced-contact-form {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 25px rgba(15, 23, 42, 0.08);
}

.light .form-title {
  color: #1e293b;
}

.light .form-subtitle {
  color: #64748b;
}

.light .enhanced-form-label {
  color: #475569;
}

.light .enhanced-form-input,
.light .enhanced-form-textarea {
  background: rgba(30, 41, 59, 0.08);
  border: 1px solid rgba(255, 1, 79, 0.25);
  color: #1e293b;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.light .enhanced-form-input:focus,
.light .enhanced-form-textarea:focus {
  border-color: rgba(255, 1, 79, 0.6);
  background: rgba(30, 41, 59, 0.12);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 0 20px rgba(255, 1, 79, 0.15);
  transform: translateY(-2px);
}

.light .enhanced-form-input::placeholder,
.light .enhanced-form-textarea::placeholder {
  color: #64748b;
}

.light .enhanced-form-input:focus::placeholder,
.light .enhanced-form-textarea:focus::placeholder {
  color: #475569;
}

/* ===== LIGHT THEME HEADER SECTION ===== */

.light .enhanced-header-title {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.5rem;
  font-weight: 700;
}

.light .enhanced-header-title-mobile {
  background: linear-gradient(135deg, #1e293b 0%, #ff014f 50%, #1e293b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.25rem;
  font-weight: 700;
}

.light .nav-link-dash {
  color: #64748b;
  transition: all 0.3s ease;
}

.light .nav-link-dash:hover {
  color: #1e293b;
  background: rgba(255, 1, 79, 0.1);
}

.light .nav-link-dash.active {
  color: #ff014f;
  background: rgba(255, 1, 79, 0.1);
}

/* Light Theme Mobile Menu */
.light .mobile-profile-container {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.light .mobile-profile-name {
  color: #1e293b;
}

.light .mobile-profile-title {
  color: #ff014f;
}

.light .mobile-profile-subtitle {
  color: #64748b;
}

.light .mobile-nav-item button {
  color: #64748b;
  border: 1px solid transparent;
}

.light .mobile-nav-item button:hover {
  color: #1e293b;
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(226, 232, 240, 0.8);
}

.light .mobile-nav-item button.nav-item-active {
  background: rgba(255, 1, 79, 0.1);
  color: #ff014f;
  border-color: rgba(255, 1, 79, 0.3);
}

.light .mobile-nav-item .bg-gray-800\/50 {
  background: rgba(248, 250, 252, 0.8);
}

.light .mobile-nav-item .text-gray-400 {
  color: #64748b;
}

.light .mobile-nav-item .group-hover\:bg-red\/20 {
  background: rgba(255, 1, 79, 0.1);
}

.light .mobile-nav-item .group-hover\:text-red {
  color: #ff014f;
}

/* ===== THEME TOGGLE STYLES ===== */

.theme-toggle-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle-btn {
  position: relative;
  cursor: pointer;
  user-select: none;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle-btn:hover {
  transform: scale(1.05);
}

.theme-toggle-btn:active {
  transform: scale(0.95);
}

.theme-toggle-btn:focus {
  outline: none;
}

/* Theme Toggle Label */
.theme-toggle-label {
  font-weight: 500;
  transition: all 0.3s ease;
  user-select: none;
}

/* Dark theme toggle label */
.dark .theme-toggle-label {
  color: #d1d5db;
}

/* Light theme toggle label */
.light .theme-toggle-label {
  color: #475569;
}

/* Theme Toggle Animations */
@keyframes toggleSlide {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Responsive Theme Toggle */
@media (max-width: 640px) {
  .theme-toggle-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .theme-toggle-label {
    font-size: 0.75rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .theme-toggle-btn {
    width: 3.5rem;
    height: 1.75rem;
  }

  .theme-toggle-btn > div:first-child + div {
    width: 1.25rem;
    height: 1.25rem;
  }
}
