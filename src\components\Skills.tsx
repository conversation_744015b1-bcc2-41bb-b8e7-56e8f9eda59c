import React, { useEffect, useRef } from "react";

const Skills = () => {
  // Animation refs for skill bars
  const skillRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.3,
    };

    const handleIntersect = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const skillItem = entry.target as HTMLElement;
          const progressBar = skillItem.querySelector(
            ".skill-bar-fill"
          ) as HTMLElement;
          const counter = skillItem.querySelector(
            ".counter-number"
          ) as HTMLElement;
          const skillBarGlow = skillItem.querySelector(
            ".skill-bar-glow"
          ) as HTMLElement;
          const percentage = parseInt(
            counter.getAttribute("data-percentage") || "0",
            10
          );

          // Add stunning animation classes
          skillItem.classList.add("skill-animating");
          progressBar.classList.add("skill-bar-animating");

          // Animate progress bar with stunning effect
          setTimeout(() => {
            progressBar.style.width = `${percentage}%`;
            progressBar.style.transition =
              "width 2.5s cubic-bezier(0.4, 0, 0.2, 1)";

            // Add glow effect during animation
            if (skillBarGlow) {
              skillBarGlow.style.opacity = "1";
              skillBarGlow.style.width = `${percentage}%`;
              skillBarGlow.style.transition =
                "width 2.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease";
            }
          }, 200);

          // Animate counter with clean integer values
          let count = 0;
          const duration = 2000; // 2 seconds total animation
          const steps = percentage; // One step per percentage point
          const stepDuration = duration / steps; // Time per step

          const timer = setInterval(() => {
            count++;
            counter.textContent = `${count}%`;

            // Add pulsing effect during counting
            counter.style.transform = `scale(${
              1 + (count / percentage) * 0.1
            })`;
            counter.style.color = `hsl(${
              340 + (count / percentage) * 20
            }, 100%, ${60 + (count / percentage) * 10}%)`;

            if (count >= percentage) {
              clearInterval(timer);
              counter.textContent = `${percentage}%`;

              // Final effect when complete
              counter.style.transform = "scale(1.1)";
              counter.style.color = "#ff014f";

              setTimeout(() => {
                counter.style.transform = "scale(1)";
                counter.style.transition = "transform 0.3s ease";
              }, 200);

              // Hide glow after animation
              setTimeout(() => {
                if (skillBarGlow) {
                  skillBarGlow.style.opacity = "0";
                }
              }, 1000);
            }
          }, stepDuration);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersect, observerOptions);

    skillRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      skillRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, []);

  // Skills data with the specified percentages
  const skills = [
    { name: "HTML & CSS", percentage: 95 },
    { name: "JavaScript", percentage: 89 },
    { name: "React.js", percentage: 90 },
    { name: "Node.js", percentage: 82 },
    { name: "WordPress", percentage: 95 },
    { name: "Figma", percentage: 88 },
    { name: "Python", percentage: 85 },
    { name: "TypeScript", percentage: 87 },
  ];

  return (
    <section
      id="skills"
      className="section bg-gradient-to-br from-white via-gray-50 to-white dark:bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="resume-bg-glow"></div>
      <div className="resume-particles"></div>
      <div className="resume-grid-pattern"></div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <h2 className="enhanced-resume-title">Professional Skills</h2>
            <div className="resume-title-glow"></div>
            <div className="resume-title-underline"></div>
          </div>

          <div className="resume-description-container">
            <p className="resume-description">
              My <span className="highlight-text">technical expertise</span> and{" "}
              <span className="highlight-text">proficiency levels</span> across
              various
              <span className="highlight-text">technologies</span> and tools
              that drive
              <span className="highlight-text">innovation</span> in web
              development.
            </p>
          </div>
        </div>

        {/* Enhanced Skills Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-10 gap-y-8">
          {skills.map((skill, index) => (
            <div
              key={index}
              ref={(el) => (skillRefs.current[index] = el)}
              className="enhanced-skill-item"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="skill-header">
                <h4 className="skill-name">{skill.name}</h4>
                <span
                  className="skill-percentage counter-number"
                  data-percentage={skill.percentage}
                >
                  0%
                </span>
              </div>

              <div className="skill-bar-container">
                <div className="skill-bar-bg">
                  <div
                    className="skill-bar-fill progress-bar"
                    style={{ width: "0%" }}
                    data-percentage={skill.percentage}
                  ></div>
                  <div className="skill-bar-glow"></div>
                </div>
              </div>

              <div className="skill-particles"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skills;
