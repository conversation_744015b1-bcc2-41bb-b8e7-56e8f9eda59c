import React, { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  Users,
  Search,
  Filter,
  MoreVertical,
  UserPlus,
  Edit,
  Trash2,
  Shield,
  ShieldCheck,
  Mail,
  Calendar,
  Activity,
  Eye,
  Ban,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";

const AdminUsers = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterRole, setFilterRole] = useState("all");

  const [users] = useState([
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      lastLogin: "2024-01-15",
      joinDate: "2023-06-15",
      avatar: "/api/placeholder/40/40",
      interactions: 45,
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "user",
      status: "active",
      lastLogin: "2024-01-14",
      joinDate: "2023-08-22",
      avatar: "/api/placeholder/40/40",
      interactions: 23,
    },
    {
      id: 3,
      name: "Mike Wilson",
      email: "<EMAIL>",
      role: "moderator",
      status: "inactive",
      lastLogin: "2024-01-10",
      joinDate: "2023-09-05",
      avatar: "/api/placeholder/40/40",
      interactions: 67,
    },
    {
      id: 4,
      name: "Emily Davis",
      email: "<EMAIL>",
      role: "user",
      status: "active",
      lastLogin: "2024-01-15",
      joinDate: "2023-11-12",
      avatar: "/api/placeholder/40/40",
      interactions: 12,
    },
    {
      id: 5,
      name: "Alex Brown",
      email: "<EMAIL>",
      role: "user",
      status: "banned",
      lastLogin: "2024-01-08",
      joinDate: "2023-12-01",
      avatar: "/api/placeholder/40/40",
      interactions: 8,
    },
  ]);

  const userStats = [
    {
      title: "Total Users",
      value: "1,247",
      change: "+12.5%",
      icon: Users,
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "Active Users",
      value: "1,089",
      change: "+8.2%",
      icon: Activity,
      color: "from-green-500 to-green-600",
    },
    {
      title: "New This Month",
      value: "156",
      change: "+23.1%",
      icon: UserPlus,
      color: "from-purple-500 to-purple-600",
    },
    {
      title: "Banned Users",
      value: "12",
      change: "-5.3%",
      icon: Ban,
      color: "from-red to-red/80",
    },
  ];

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-red/20 text-red border-red/30";
      case "moderator":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      case "user":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "inactive":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "banned":
        return "bg-red/20 text-red border-red/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const handleUserAction = (action: string, userId: number) => {
    switch (action) {
      case "edit":
        toast.info(`Edit user ${userId} - Feature coming soon!`);
        break;
      case "delete":
        toast.error(`Delete user ${userId} - Feature coming soon!`);
        break;
      case "ban":
        toast.warning(`Ban user ${userId} - Feature coming soon!`);
        break;
      case "promote":
        toast.success(`Promote user ${userId} - Feature coming soon!`);
        break;
      default:
        break;
    }
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === "all" || user.role === filterRole;
    return matchesSearch && matchesRole;
  });

  return (
    <AdminLayout title="User Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-white">User Management</h2>
            <p className="text-gray-400">Manage user accounts and permissions</p>
          </div>
          <Button className="bg-red hover:bg-red/80">
            <UserPlus className="w-4 h-4 mr-2" />
            Add New User
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {userStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="bg-dark-lighter border border-red/20 rounded-xl p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-green-400 text-sm font-medium">
                    {stat.change}
                  </span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Filters and Search */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-dark-bg border-red/30 text-white"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={filterRole}
                onChange={(e) => setFilterRole(e.target.value)}
                className="px-4 py-2 bg-dark-bg border border-red/30 rounded-lg text-white"
              >
                <option value="all">All Roles</option>
                <option value="admin">Admin</option>
                <option value="moderator">Moderator</option>
                <option value="user">User</option>
              </select>
              <Button variant="outline" className="border-red/30 text-red hover:bg-red/10">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-dark-bg/50 border-b border-red/20">
                <tr>
                  <th className="text-left p-4 text-gray-300 font-medium">User</th>
                  <th className="text-left p-4 text-gray-300 font-medium">Role</th>
                  <th className="text-left p-4 text-gray-300 font-medium">Status</th>
                  <th className="text-left p-4 text-gray-300 font-medium">Last Login</th>
                  <th className="text-left p-4 text-gray-300 font-medium">Interactions</th>
                  <th className="text-left p-4 text-gray-300 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="border-b border-red/10 hover:bg-dark-bg/30">
                    <td className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center">
                          <span className="text-white font-medium">
                            {user.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-white font-medium">{user.name}</p>
                          <p className="text-gray-400 text-sm">{user.email}</p>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge className={getRoleColor(user.role)}>
                        {user.role === "admin" && <ShieldCheck className="w-3 h-3 mr-1" />}
                        {user.role === "moderator" && <Shield className="w-3 h-3 mr-1" />}
                        {user.role}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <Badge className={getStatusColor(user.status)}>
                        {user.status}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2 text-gray-300">
                        <Calendar className="w-4 h-4" />
                        {user.lastLogin}
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2 text-gray-300">
                        <Activity className="w-4 h-4" />
                        {user.interactions}
                      </div>
                    </td>
                    <td className="p-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-dark-lighter border-red/20">
                          <DropdownMenuItem onClick={() => handleUserAction("edit", user.id)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleUserAction("promote", user.id)}>
                            <Shield className="w-4 h-4 mr-2" />
                            Change Role
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleUserAction("ban", user.id)}>
                            <Ban className="w-4 h-4 mr-2" />
                            Ban User
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleUserAction("delete", user.id)}
                            className="text-red hover:text-red"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminUsers;
