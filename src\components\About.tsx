import React from "react";

const About = () => {
  // Features data
  const features = [
    {
      title: "Business Strategy",
      description:
        "There are many variations of passages of Lorem Ipsum available.",
      icon: "💼",
    },
    {
      title: "Website Development",
      description:
        "There are many variations of passages of Lorem Ipsum available.",
      icon: "💻",
    },
    {
      title: "Marketing & Reporting",
      description:
        "There are many variations of passages of Lorem Ipsum available.",
      icon: "📊",
    },
  ];

  return (
    <section
      id="about"
      className="section bg-gradient-to-br from-white via-gray-50 to-white dark:bg-dark-darker relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="about-bg-glow"></div>
      <div className="about-particles"></div>

      <div className="container mx-auto relative z-10">
        <div className="flex flex-col md:flex-row gap-12 lg:gap-16">
          {/* About Content */}
          <div className="w-full md:w-1/2">
            {/* Enhanced Title */}
            <div className="relative mb-8">
              <h2 className="enhanced-section-title">About Me</h2>
              <div className="title-glow-effect"></div>
              <div className="title-underline-animated"></div>
            </div>

            {/* Enhanced Description */}
            <div className="space-y-6">
              <div className="about-text-block">
                <p className="about-description text-lg leading-relaxed">
                  I'm a web developer with a passion for creating{" "}
                  <span className="highlight-keyword">beautiful</span>,
                  <span className="highlight-keyword">functional</span>, and{" "}
                  <span className="highlight-keyword">user-friendly</span>{" "}
                  websites. I have{" "}
                  <span className="highlight-number">8+ years</span> of
                  experience in various technologies including
                  <span className="tech-highlight">React</span>,{" "}
                  <span className="tech-highlight">Next.js</span>,
                  <span className="tech-highlight">Node.js</span>, and more.
                </p>
              </div>

              <div className="about-text-block">
                <p className="about-description text-lg leading-relaxed">
                  I help companies build{" "}
                  <span className="highlight-keyword">great products</span>{" "}
                  through my expertise in
                  <span className="highlight-keyword">
                    frontend and backend development
                  </span>
                  , focusing on{" "}
                  <span className="highlight-keyword">performance</span>,
                  <span className="highlight-keyword">accessibility</span>, and
                  <span className="highlight-keyword">
                    modern design principles
                  </span>
                  .
                </p>
              </div>
            </div>

            {/* Enhanced Personal Info Grid */}
            <div className="grid grid-cols-2 gap-6 mt-10">
              <div className="info-item">
                <h6 className="info-label">Name:</h6>
                <p className="info-value">John Smith</p>
                <div className="info-glow"></div>
              </div>
              <div className="info-item">
                <h6 className="info-label">Email:</h6>
                <p className="info-value"><EMAIL></p>
                <div className="info-glow"></div>
              </div>
              <div className="info-item">
                <h6 className="info-label">Phone:</h6>
                <p className="info-value">+************</p>
                <div className="info-glow"></div>
              </div>
              <div className="info-item">
                <h6 className="info-label">Location:</h6>
                <p className="info-value">New York, USA</p>
                <div className="info-glow"></div>
              </div>
            </div>
          </div>

          {/* Enhanced Features */}
          <div className="w-full md:w-1/2">
            <div className="grid grid-cols-1 gap-8">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="enhanced-feature-card group"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="feature-icon-container">
                    <div className="feature-icon">{feature.icon}</div>
                    <div className="icon-glow"></div>
                    <div className="icon-particles"></div>
                  </div>

                  <div className="feature-content">
                    <h3 className="feature-title">{feature.title}</h3>
                    <p className="feature-description">{feature.description}</p>
                  </div>

                  <div className="card-glow-effect"></div>
                  <div className="card-border-animation"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
