import React from "react";
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Download,
  MapPin,
  Calendar,
  <PERSON>rkles,
  Star,
} from "lucide-react";

const Sidebar = () => {
  return (
    <div className="bg-dark-lighter w-full lg:w-[380px] lg:h-screen lg:fixed lg:top-0 lg:left-0 lg:overflow-y-auto relative">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-red/5 via-transparent to-purple-500/5 pointer-events-none"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,1,79,0.03)_0%,transparent_70%)] pointer-events-none"></div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-8 w-1 h-1 bg-red/40 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-12 w-1.5 h-1.5 bg-purple-400/30 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-60 left-16 w-0.5 h-0.5 bg-blue-400/40 rounded-full animate-pulse delay-2000"></div>
        <div className="absolute bottom-40 right-8 w-1 h-1 bg-red/30 rounded-full animate-pulse delay-3000"></div>
        <div className="absolute bottom-60 left-12 w-1.5 h-1.5 bg-purple-400/20 rounded-full animate-pulse delay-500"></div>
      </div>

      <div className="flex flex-col min-h-full relative z-10 p-8">
        {/* Enhanced Profile Image and Name */}
        <div className="text-center mb-8 relative">
          {/* Profile Image with Enhanced Effects */}
          <div className="relative mx-auto w-40 h-40 mb-5 group">
            {/* Outer Glow Ring */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red via-white to-blue-500 p-1 animate-spin-slow">
              <div className="w-full h-full rounded-full bg-dark-lighter"></div>
            </div>

            {/* Main Image Container */}
            <div className="absolute inset-2 rounded-full overflow-hidden ring-4 ring-red/30 group-hover:ring-red/50 transition-all duration-500">
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&auto=format&q=80"
                alt="John Smith"
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />

              {/* Image Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-red/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>

            {/* Floating Stars */}
            <Star className="absolute -top-2 -right-2 w-4 h-4 text-yellow-400 animate-pulse" />
            <Sparkles className="absolute -bottom-1 -left-2 w-3 h-3 text-purple-400 animate-pulse delay-1000" />
          </div>

          {/* Enhanced Name and Title */}
          <div className="relative">
            <h1 className="text-white text-2xl font-bold mb-2 bg-gradient-to-r from-white via-red to-white bg-clip-text text-transparent bg-300% animate-gradient-x">
              John Smith
            </h1>
            <div className="relative">
              <div className="typing-text mt-2 text-gray-300 text-center mx-auto max-w-[220px] text-base font-medium">
                <span className="bg-gradient-to-r from-gray-300 to-red bg-clip-text text-transparent">
                  Web Developer
                </span>
              </div>
              {/* Underline Effect */}
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-transparent via-red to-transparent animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Enhanced Social Icons */}
        <div className="flex justify-center gap-3 mb-8">
          <a href="#" className="enhanced-social-icon group">
            <div className="relative">
              <Facebook
                size={20}
                className="relative z-10 transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-blue-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
            </div>
          </a>
          <a href="#" className="enhanced-social-icon group">
            <div className="relative">
              <Twitter
                size={20}
                className="relative z-10 transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-sky-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
            </div>
          </a>
          <a href="#" className="enhanced-social-icon group">
            <div className="relative">
              <Instagram
                size={20}
                className="relative z-10 transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
            </div>
          </a>
          <a href="#" className="enhanced-social-icon group">
            <div className="relative">
              <Linkedin
                size={20}
                className="relative z-10 transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-blue-600/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
            </div>
          </a>
        </div>

        {/* Enhanced Contact Details */}
        <div className="space-y-4 mb-8">
          {/* Location Card */}
          <div className="enhanced-contact-card group">
            <div className="relative">
              {/* Background Glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative flex items-center gap-4 p-4">
                <div className="relative">
                  {/* Icon Container */}
                  <div className="min-w-12 h-12 rounded-xl bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center text-green-400 group-hover:scale-110 transition-transform duration-300 border border-green-500/20">
                    <MapPin size={20} className="relative z-10" />
                    {/* Icon Glow */}
                    <div className="absolute inset-0 bg-green-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  {/* Floating Dot */}
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>

                <div className="flex-1">
                  <p className="text-sm text-gray-200 mb-1 font-medium">
                    Location
                  </p>
                  <p className="text-gray-100 font-semibold text-base group-hover:text-green-400 transition-colors duration-300">
                    New York, USA
                  </p>
                </div>

                {/* Decorative Element */}
                <div className="w-1 h-8 bg-gradient-to-b from-green-500/50 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>

          {/* Birthday Card */}
          <div className="enhanced-contact-card group">
            <div className="relative">
              {/* Background Glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative flex items-center gap-4 p-4">
                <div className="relative">
                  {/* Icon Container */}
                  <div className="min-w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center text-purple-400 group-hover:scale-110 transition-transform duration-300 border border-purple-500/20">
                    <Calendar size={20} className="relative z-10" />
                    {/* Icon Glow */}
                    <div className="absolute inset-0 bg-purple-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  {/* Floating Sparkle */}
                  <Sparkles className="absolute -top-1 -right-1 w-3 h-3 text-pink-400 animate-pulse" />
                </div>

                <div className="flex-1">
                  <p className="text-sm text-gray-200 mb-1 font-medium">
                    Birthday
                  </p>
                  <p className="text-gray-100 font-semibold text-base group-hover:text-purple-400 transition-colors duration-300">
                    May 27, 1990
                  </p>
                </div>

                {/* Decorative Element */}
                <div className="w-1 h-8 bg-gradient-to-b from-purple-500/50 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Download CV Button */}
        <div className="mt-auto">
          <div className="relative group">
            {/* Button Glow Background */}
            <div className="absolute inset-0 bg-gradient-to-r from-red via-purple-500 to-red rounded-xl blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-500 animate-pulse"></div>

            <a
              href="#"
              className="relative block w-full bg-gradient-to-r from-red to-red-600 hover:from-red-600 hover:to-red text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-red/25 border border-red/20 hover:border-red/40"
            >
              <div className="flex items-center justify-center gap-3">
                <div className="relative">
                  <Download
                    size={20}
                    className="transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"
                  />
                  {/* Icon Glow */}
                  <div className="absolute inset-0 bg-white/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <span className="text-lg font-bold tracking-wide">
                  Download CV
                </span>

                {/* Animated Arrow */}
                <div className="w-0 group-hover:w-6 transition-all duration-300 overflow-hidden">
                  <div className="text-white/80">→</div>
                </div>
              </div>

              {/* Button Shine Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
